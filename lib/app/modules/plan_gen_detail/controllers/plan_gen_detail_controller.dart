import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/net/net.dart';

class PlanGenDetailController extends GetxController {
  Rx<LearningPlanResp?> learningPlanResp = Rx<LearningPlanResp?>(null);
  var startLevel = "".obs;
  var targetLevel = "".obs;
  var stageEndDates = <String>[].obs;
  var stageDescriptions = <String>[].obs;
  @override
  void onInit() {
    super.onInit();
    Net.getRestClient().currentPlan().then((value) {
      learningPlanResp.value = value.data;
      if (value.data?.stages == null) {
        return;
      }

      // 获取开始和目标级别
      startLevel.value = learningPlanResp.value!.startLevel;
      targetLevel.value = learningPlanResp.value!.targetLevel;

      // 从学习计划中提取阶段信息
      for (final stage in learningPlanResp.value!.stages ?? []) {
        if (stage.endDate != null && stage.endDate!.isNotEmpty) {
          try {
            final date = DateTime.parse(stage.endDate!);
            stageEndDates.add(DateFormat('MM/dd').format(date));
          } catch (e) {
            stageEndDates.add(stage.endDate!);
          }
        } else {
          stageEndDates.add('未知');
        }

        stageDescriptions.add(stage.stageDesc);
      }
      debugPrint("stageDescriptions=${stageDescriptions}");
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}

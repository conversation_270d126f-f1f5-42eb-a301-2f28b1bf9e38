import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import '../../guide/widgets/level_chart.dart';
import '../controllers/plan_gen_detail_controller.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';

class PlanGenDetailView extends GetView<PlanGenDetailController> {
  const PlanGenDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PlanGenDetailView'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
                width: Get.width,
                height: 300,
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Obx(
                    () => CustomPaint(
                      painter: ChartPainter(
                        startLevel: controller.startLevel.value,
                        targetLevel: controller.targetLevel.value,
                        stageEndDates: controller.stageEndDates,
                        stageDescriptions: controller.stageDescriptions,
                        highlightedIndex: controller.stageDescriptions.length - 1,
                        curveColor: Get.theme.primaryColor,
                        curveWidth: 4.0,
                        tipsBorderRadius: 10.0,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Obx(
              () => ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                itemCount: controller.learningPlanResp.value?.stages?.length ?? 0,
                itemBuilder: (context, index) {
                  final stage = controller.learningPlanResp.value!.stages![index];

                  // 格式化日期
                  String dateText = '';
                  if (stage.endDate != null && stage.endDate!.isNotEmpty) {
                    try {
                      final endDate = DateTime.parse(stage.endDate!);
                      final now = DateTime.now();
                      final startDate = DateTime.parse(stage.startDate!);

                      final isStartToday = startDate.year == now.year && startDate.month == now.month && startDate.day == now.day;

                      if (isStartToday) {
                        // 如果开始时间是今天，显示为"今天-结束日期"
                        final endDateFormatted = "${endDate.year}年${endDate.month}月${endDate.day}日";
                        dateText = "今天-$endDateFormatted";
                      } else {
                        // 否则正常显示时间格式
                        dateText = "${startDate.year}年${startDate.month}月${startDate.day}日 - ${endDate.year}年${endDate.month}月${endDate.day}日";
                      }
                    } catch (e) {
                      dateText = stage.endDate!;
                    }
                  }

                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          stage.stageDesc,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          dateText,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                        Gap(8.whs),
                        Text(
                          stage.objective,
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[800],
                          ),
                        ),
                        Gap(8.whs),
                        if (stage.resources.isNotEmpty) ...[
                          Gap(16.whs),
                          Gap(8.whs),
                          ...stage.resources
                              .map((resource) => Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: Row(
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Container(
                                            width: 60,
                                            height: 60,
                                            color: Colors.grey[300],
                                            child: resource.resourceCover != null
                                                ? ImageLoader(
                                                    resource.resourceCover!,
                                                    width: 60,
                                                    height: 60,
                                                    fit: BoxFit.cover,
                                                  )
                                                : const Icon(Icons.video_library, color: Colors.grey),
                                          ),
                                        ),
                                        Gap(12.whs),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                resource.resourceName,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              Gap(4.whs),
                                              Row(
                                                children: [
                                                  const Icon(Icons.timer, size: 16, color: Colors.grey),
                                                  Gap(4.whs),
                                                  Text('- h - min - ${resource.lsCount}ls', style: TextStyle(color: Colors.grey[600])),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ))
                              .toList(),
                        ],
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

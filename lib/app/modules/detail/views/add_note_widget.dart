import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/app/modules/detail/views/ai_chat_widget.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/theme.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/toast.dart';

import '../../../../widgets/split_english.dart';

//0无效 1收藏 2保存笔记
typedef NoteSaveCallback = void Function(int);

class AddNoteWidget extends StatefulWidget {
  final Subtitle subtitle;
  final int resourceType;
  final String resourceId;
  final String noteId;
  final int videoStartTime;
  final int videoEndTime;
  final NoteSaveCallback noteSaveCallback;

  const AddNoteWidget({
    super.key,
    required this.subtitle,
    required this.resourceType,
    required this.resourceId,
    required this.noteId,
    required this.noteSaveCallback,
    required this.videoStartTime,
    required this.videoEndTime,
  });

  @override
  State<AddNoteWidget> createState() => _AddNoteWidgetState();
}

class _AddNoteWidgetState extends State<AddNoteWidget> {
  final FocusNode _focusNode = FocusNode();
  TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    textEditingController.addListener(() {
      setState(() {});
    });
    if (widget.noteId.isNotEmpty) {
      Net.getRestClient().localNote(widget.noteId).then((value) {
        textEditingController.text = value.data.content ?? "";
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    textEditingController.dispose();
  }

  void saveNote() {
    if (textEditingController.text.isEmpty) {
      "请输入笔记内容".toast;
      return;
    }
    Net.getRestClient().addNote({
      'id': widget.noteId,
      'resourceId': widget.resourceId,
      'resourceType': widget.resourceType,
      'content': textEditingController.text,
      'videoStartTime': widget.videoStartTime,
      'videoEndTime': widget.videoEndTime,
    }).then((value) {
      widget.noteSaveCallback(2);
      Get.back();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 70.whs),
      child: DraggableScrollableSheet(
        initialChildSize: 1,
        expand: true,
        snap: true,
        builder: (BuildContext context, ScrollController scrollController) {
          return NotificationListener<DraggableScrollableNotification>(
            onNotification: (notification) {
              if (notification.extent <= 0.3) {
                if (Get.isBottomSheetOpen ?? false) {
                  Get.back();
                }
              }
              return true;
            },
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Container(
                height: MediaQuery.of(context).size.height * 0.9,
                color: Get.theme.scaffoldBackgroundColor,
                padding: EdgeInsets.only(bottom: 34.whs),
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.whs, horizontal: 14.whs),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: const Icon(Icons.close),
                          ),
                          const Spacer(),
                          Text(
                            "记笔记",
                            style: TextStyle(
                              fontSize: 16.whs,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xff555454),
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 14.whs, vertical: 6.whs),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Get.theme.primaryColor.withOpacity(0.2),
                                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                                child: SplitEnglishWidget(
                                  targetLanguageStyle: TextStyle(color: Get.theme.primaryColor, fontSize: 16.whs),
                                  subtitle: widget.subtitle,
                                  enableTranslate: false,
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.whs),
                              child: TextField(
                                focusNode: _focusNode,
                                decoration: const InputDecoration(
                                  hintText: "输入笔记内容...",
                                  contentPadding: EdgeInsets.zero,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                ),
                                controller: textEditingController,
                                cursorColor: Get.theme.primaryColor,
                                keyboardType: TextInputType.multiline,
                                maxLines: null, // Allows the TextField to grow as per the content
                                textInputAction: TextInputAction.newline,
                                style: TextStyle(
                                  fontSize: 16.whs,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            Get.to(AiChatWidget(
                              subtitle: widget.subtitle,
                            ));
                            // Get.bottomSheet(
                            //   AiSentenceResultWidget(
                            //     subtitle: widget.subtitle,
                            //     isInputEmpty: textEditingController.text == "",
                            //     aiSentenceSaveCallback: (data, type) {
                            //       Get.back();
                            //       setState(() {
                            //         if (type == 1) {
                            //           textEditingController.text = data;
                            //         } else if (type == 2) {
                            //           var oldText = textEditingController.text;
                            //           textEditingController.text = "$oldText\n$data";
                            //         }
                            //       });
                            //     },
                            //   ),
                            //   shape: const RoundedRectangleBorder(
                            //     borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                            //   ),
                            //   isDismissible: false,
                            //   isScrollControlled: true,
                            //   enableDrag: false,
                            //   barrierColor: Colors.transparent,
                            // );
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Get.theme.primaryColor.withOpacity(0.2),
                                borderRadius: const BorderRadius.all(Radius.circular(8)),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                              child: Text(
                                "AI解析",
                                style: TextStyle(color: Get.theme.primaryColor, fontSize: 16.whs),
                              ),
                            ),
                          ),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                color: gray700.withOpacity(0.1),
                                borderRadius: const BorderRadius.all(Radius.circular(8)),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                              child: Text(
                                "收藏",
                                style: TextStyle(color: gray700, fontSize: 16.whs, fontWeight: FontWeight.w500),
                              ),
                            ),
                            Gap(10.whs),
                            GestureDetector(
                              onTap: () => saveNote(),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: gray700.withOpacity(0.1),
                                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 12.whs, vertical: 6.whs),
                                child: Text(
                                  "保存",
                                  style: TextStyle(
                                      color: textEditingController.text.isEmpty ? gray400 : gray700, fontSize: 16.whs, fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                            Gap(16.whs),
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

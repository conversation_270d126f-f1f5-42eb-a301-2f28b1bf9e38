import 'package:lsenglish/utils/type.dart';

import '../../../../model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/model/speech_evaluation_resp.dart';
import 'package:lsenglish/utils/oss.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';

bool shouldUseDoubleSubtitle(ResourceDetailResp? localDetailResp) {
  return localDetailResp?.resourceType == ResourceType.remoteResouce.index && localDetailResp?.subtitleUrl == "";
}

/// 过滤视频URL，移除查询参数部分
/// 例如：将 https://example.com/video.mp4?param1=value1&param2=value2
/// 转换为 https://example.com/video.mp4
String filterVideoUrl(String url) {
  if (url.isEmpty) return url;

  try {
    final uri = Uri.parse(url);
    return '${uri.scheme}://${uri.host}${uri.path}';
  } catch (e) {
    return url;
  }
}

/// 批量上传本地录音到OSS并同步到后端
Future<void> batchUploadSpeechEvaluationsToOssAndSync(Map<int, SpeechEvaluationResp> speechEvaluationMap) async {
  try {
    List<Map<String, dynamic>> evaluations = [];
    for (var entry in speechEvaluationMap.entries) {
      var eval = entry.value;
      String? audioUrl = eval.audioUrl;
      if (eval.localAudioPath != null && eval.localAudioPath!.isNotEmpty) {
        String? ossUrl = await OssUtil().uploadUserRecordFile(eval.localAudioPath!);
        if (ossUrl != null) {
          audioUrl = ossUrl;
        }
      }
      if (audioUrl != null && eval.id != null) {
        evaluations.add({
          'id': eval.id,
          'audioUrl': audioUrl,
        });
      }
    }
    if (evaluations.isNotEmpty) {
      await Net.getRestClient().batchUpdateSpeechEvaluationAudioUrl({'evaluations': evaluations});
    }
  } catch (e) {
    logger('批量上传语音评测音频失败: $e');
  }
}

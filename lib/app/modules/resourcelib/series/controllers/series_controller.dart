import 'package:get/get.dart';
import 'package:lsenglish/model/resource_resp/resource_resp.dart';
import 'package:lsenglish/model/series_resp/series_detail_resp.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/routes.dart';

class SeriesController extends GetxController {
  var seriesDetailResp = SeriesDetailResp().obs;
  var resources = <ResourceResp>[].obs;
  @override
  void onInit() {
    super.onInit();
    var seriesId = Get.arguments['seriesId'] ?? "";
    Net.getRestClient().getResourceBySeriesId(seriesId).then((onValue) {
      seriesDetailResp.value = onValue.data;
      resources.value = onValue.data.resources ?? [];
    });
  }

  void onResourceItemClick(int index) {
    RoutesUtil().goVideoDetailByRemote(resources[index].videoUrl, resources[index].defaultLangTitle, resources[index].id);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}

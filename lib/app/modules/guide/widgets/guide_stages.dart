import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'dart:ui' show lerpDouble;
import '../controllers/guide_controller.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;

import 'level_chart.dart';
import 'prediction_chart_painter.dart';

class WelcomeStage extends StatelessWidget {
  const WelcomeStage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Spacer(),
        Text(
          '我将会问你一些问题\n来制定你的个人目标计划',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Expanded(child: Icon(Icons.rocket_launch, size: 80.whs)),
        Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: () {
              // 进入下一阶段
              Get.find<GuideController>().nextStage();
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: const Color(0xff101828),
              foregroundColor: Colors.white,
            ),
            child: const Text('开始'),
          ),
        ),
      ],
    );
  }
}

class ChoiceStage extends StatelessWidget {
  final String title;
  final List<String> choices;
  final int? selectedIndex;
  final Function(int) onSelect;
  final String stageKey;

  const ChoiceStage({
    super.key,
    required this.title,
    required this.choices,
    required this.selectedIndex,
    required this.onSelect,
    required this.stageKey,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Gap(60.whs),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: choices.length,
            itemBuilder: (context, index) {
              final isSelected = selectedIndex == index;
              return AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withAlpha(25) : Colors.white,
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ListTile(
                  title: Text(choices[index]),
                  trailing: AnimatedOpacity(
                    duration: const Duration(milliseconds: 500),
                    opacity: isSelected ? 1.0 : 0.0,
                    child: Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
                  ),
                  onTap: () => onSelect(index),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class MultiChoiceStage extends StatelessWidget {
  final String title;
  final List<String> choices;
  final List<int> selectedIndices;
  final Function(int) onToggle;
  final Function() onConfirm;
  final String stageKey;

  const MultiChoiceStage({
    super.key,
    required this.title,
    required this.choices,
    required this.selectedIndices,
    required this.onToggle,
    required this.onConfirm,
    required this.stageKey,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Gap(20.whs),
        Text(
          '(可多选)',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        Gap(30.whs),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: choices.length,
            itemBuilder: (context, index) {
              final isSelected = selectedIndices.contains(index);
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withAlpha(25) : Colors.white,
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ListTile(
                  title: Text(choices[index]),
                  trailing: AnimatedOpacity(
                    duration: const Duration(milliseconds: 300),
                    opacity: isSelected ? 1.0 : 0.0,
                    child: Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
                  ),
                  onTap: () => onToggle(index),
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: selectedIndices.isNotEmpty ? onConfirm : null,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: const Color(0xff101828),
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              disabledBackgroundColor: Colors.grey[300],
            ),
            child: const Text('确认'),
          ),
        ),
      ],
    );
  }
}

class LevelChartStage extends StatefulWidget {
  final String title;
  final Function(int) onConfirm;
  final String buttonText;

  const LevelChartStage({
    super.key,
    required this.title,
    required this.onConfirm,
    this.buttonText = '确认',
  });

  @override
  State<LevelChartStage> createState() => _LevelChartStageState();
}

class _LevelChartStageState extends State<LevelChartStage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int selectedIndex = 0;
  int _targetIndex = 0;
  double _currentPosition = 0;
  final List<String> levels = ['A0', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
  bool _isDragging = false;
  double _dragStartPosition = 0;
  double _dragStartIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentPosition = selectedIndex.toDouble();
    _targetIndex = selectedIndex;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = _animationController.drive(CurveTween(curve: Curves.easeInOut));
    _animationController.addListener(() {
      if (!_isDragging) {
        setState(() {
          _currentPosition = lerpDouble(selectedIndex.toDouble(), _targetIndex.toDouble(), _animation.value)!;
        });
      }
    });
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          selectedIndex = _targetIndex;
          _currentPosition = _targetIndex.toDouble();
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapOrDrag(Offset position, BoxConstraints constraints) {
    const horizontalPadding = 36.0;
    final availableWidth = constraints.maxWidth - (horizontalPadding * 2);
    final adjustedX = position.dx - horizontalPadding;

    if (adjustedX < 0 || adjustedX > availableWidth) {
      return;
    }

    final newIndex = (adjustedX / (availableWidth / (levels.length - 1))).round();
    if (newIndex >= 0 && newIndex < levels.length) {
      setState(() {
        _targetIndex = newIndex;
        if (_isDragging) {
          _currentPosition = newIndex.toDouble();
        }
      });
      if (!_isDragging) {
        _animationController.forward(from: 0);
      }
    }
  }

  void _handleDragStart(Offset position, BoxConstraints constraints) {
    const horizontalPadding = 36.0;
    final availableWidth = constraints.maxWidth - (horizontalPadding * 2);
    final adjustedX = position.dx - horizontalPadding;

    if (adjustedX < 0 || adjustedX > availableWidth) {
      return;
    }

    setState(() {
      _isDragging = true;
      _dragStartPosition = adjustedX;
      _dragStartIndex = _currentPosition;
    });
  }

  void _handleDragUpdate(Offset position, BoxConstraints constraints) {
    if (!_isDragging) return;

    const horizontalPadding = 36.0;
    final availableWidth = constraints.maxWidth - (horizontalPadding * 2);
    final adjustedX = position.dx - horizontalPadding;

    if (adjustedX < 0 || adjustedX > availableWidth) {
      return;
    }

    final dragDistance = adjustedX - _dragStartPosition;
    final indexChange = dragDistance / (availableWidth / (levels.length - 1));
    final newPosition = _dragStartIndex + indexChange;

    setState(() {
      _currentPosition = newPosition.clamp(0.0, (levels.length - 1).toDouble());
      _targetIndex = _currentPosition.round().clamp(0, levels.length - 1);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          widget.title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Expanded(
          child: Center(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return GestureDetector(
                  onTapDown: (details) => _handleTapOrDrag(details.localPosition, constraints),
                  onHorizontalDragStart: (details) => _handleDragStart(details.localPosition, constraints),
                  onHorizontalDragUpdate: (details) => _handleDragUpdate(details.localPosition, constraints),
                  onHorizontalDragEnd: (_) {
                    setState(() {
                      _isDragging = false;
                      selectedIndex = _targetIndex;
                      _currentPosition = _targetIndex.toDouble();
                    });
                  },
                  child: SizedBox(
                    width: Get.width,
                    height: 300,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 36.0),
                      child: CustomPaint(
                        size: Size(Get.width - 72, 300),
                        painter: LevelChartPainter(
                          selectedIndex: selectedIndex,
                          currentPosition: _currentPosition,
                          levels: levels,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: () {
              // 调用传入的回调函数
              widget.onConfirm(selectedIndex);
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: const Color(0xff101828),
              foregroundColor: Colors.white,
            ),
            child: Text(widget.buttonText),
          ),
        ),
      ],
    );
  }
}

class LevelChartPainter extends CustomPainter {
  final int selectedIndex;
  final double currentPosition;
  final List<String> levels;

  LevelChartPainter({
    required this.selectedIndex,
    required this.currentPosition,
    required this.levels,
  });

  double _getYForProgress(double progress, Size size, double padding) {
    final height = size.height;
    final startY = height - padding;
    final endY = padding;
    final heightDiff = startY - endY;

    // 调整起点位置，让 A0 从较高位置开始
    final initialHeight = heightDiff * 0.2; // A0 开始位置在总高度差的 40% 处
    final p0y = startY - initialHeight;

    // 调整控制点，使曲线更平滑
    final p1y = startY - heightDiff * 0.3; // 第一个控制点
    final p2y = startY - heightDiff * 0.4; // 第二个控制点
    final p3y = endY; // 终点

    final t = progress;
    return (1 - t) * (1 - t) * (1 - t) * p0y + 3 * (1 - t) * (1 - t) * t * p1y + 3 * (1 - t) * t * t * p2y + t * t * t * p3y;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    const padding = 60.0;
    const labelPadding = 20.0;
    final xAxisY = height - padding + labelPadding;
    final segmentWidth = width / (levels.length - 1);

    // 绘制底部x轴实线
    final xAxisPaint = Paint()
      ..color = const Color(0xFFB4A5FF).withAlpha(77)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, xAxisY),
      Offset(width, xAxisY),
      xAxisPaint,
    );

    // 绘制顶部x轴实线
    canvas.drawLine(
      const Offset(0, padding),
      Offset(width, padding),
      xAxisPaint,
    );

    // 绘制垂直虚线和标签
    for (int i = 0; i < levels.length; i++) {
      final labelX = i * segmentWidth;
      const dashHeight = 5.0;
      const dashSpace = 5.0;
      double startY = xAxisY;

      while (startY > padding) {
        canvas.drawLine(
          Offset(labelX, startY),
          Offset(labelX, startY - dashHeight),
          Paint()
            ..color = const Color(0xFFB4A5FF).withAlpha(77)
            ..strokeWidth = 1
            ..style = PaintingStyle.stroke,
        );
        startY -= (dashHeight + dashSpace);
      }

      // 计算当前标签与拖动位置的距离
      final distance = (i - currentPosition).abs();
      final isNearby = distance < 0.5;

      // 绘制标签
      final textPainter = TextPainter(
        text: TextSpan(
          text: levels[i],
          style: TextStyle(
            color: isNearby ? const Color(0xFF7C69FF) : Colors.grey,
            fontSize: 14,
          ),
        ),
        textDirection: ui.TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(labelX - textPainter.width / 2, xAxisY + 8),
      );
    }

    // 绘制曲线
    final curvePaint = Paint()
      ..color = const Color(0xFFB4A5FF).withAlpha(77)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final startY = height - padding;
    const endY = padding;
    final heightDiff = startY - endY;
    final initialHeight = heightDiff * 0.2;

    // 绘制曲线起点
    double pathX = 0;
    double pathY = startY - initialHeight;
    path.moveTo(pathX, pathY);

    const totalPoints = 200;
    for (int i = 1; i <= totalPoints; i++) {
      final t = i / totalPoints;
      final curveX = width * t;
      final curveY = _getYForProgress(t, size, padding);

      if ((i ~/ 3) % 2 == 0) {
        path.lineTo(curveX, curveY);
      } else {
        path.moveTo(curveX, curveY);
      }
    }

    canvas.drawPath(path, curvePaint);

    // 绘制动画元素
    final animX = currentPosition * segmentWidth;
    final animY = _getYForProgress(currentPosition / (levels.length - 1), size, padding);

    // 绘制图标
    final iconPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(Icons.arrow_drop_up.codePoint),
        style: TextStyle(
          fontSize: 50,
          height: 1,
          color: const Color(0xFF7C69FF),
          fontFamily: Icons.arrow_drop_up.fontFamily,
          package: Icons.arrow_drop_up.fontPackage,
        ),
      ),
      textDirection: ui.TextDirection.ltr,
    );
    iconPainter.layout();

    canvas.save();
    canvas.translate(
      animX - iconPainter.width / 2,
      xAxisY - iconPainter.height / 2,
    );
    iconPainter.paint(canvas, Offset.zero);
    canvas.restore();

    // 绘制气泡和连接线
    const bubbleWidth = 60.0;
    const bubbleHeight = 36.0;
    const bubbleRadius = 8.0;
    const arrowHeight = 6.0;
    const arrowWidth = 12.0;
    final bubbleTop = animY - bubbleHeight - arrowHeight - 15;

    // 绘制连接线
    canvas.drawLine(
      Offset(animX, xAxisY),
      Offset(animX, bubbleTop + bubbleHeight + arrowHeight),
      Paint()
        ..color = const Color(0xFF7C69FF)
        ..strokeWidth = 5
        ..style = PaintingStyle.stroke,
    );

    // 绘制选中点的三层圆圈
    canvas.drawCircle(
      Offset(animX, animY),
      12,
      Paint()
        ..color = const Color(0xFF7C69FF).withAlpha(128)
        ..style = PaintingStyle.fill,
    );

    canvas.drawCircle(
      Offset(animX, animY),
      8,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill,
    );

    canvas.drawCircle(
      Offset(animX, animY),
      4,
      Paint()
        ..color = const Color(0xFF7C69FF)
        ..style = PaintingStyle.fill,
    );

    // 绘制气泡
    final bubbleRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        animX - bubbleWidth / 2,
        bubbleTop,
        bubbleWidth,
        bubbleHeight,
      ),
      const Radius.circular(bubbleRadius),
    );

    canvas.drawRRect(
      bubbleRect,
      Paint()
        ..color = Colors.black.withAlpha(26)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4),
    );

    canvas.drawRRect(
      bubbleRect,
      Paint()..color = const Color(0xFF7C69FF),
    );

    // 绘制气泡三角形
    final trianglePath = Path()
      ..moveTo(animX, bubbleTop + bubbleHeight + arrowHeight)
      ..lineTo(animX - arrowWidth / 2, bubbleTop + bubbleHeight)
      ..lineTo(animX + arrowWidth / 2, bubbleTop + bubbleHeight)
      ..close();

    canvas.drawPath(
      trianglePath,
      Paint()..color = const Color(0xFF7C69FF),
    );

    // 计算当前最接近的级别
    final currentLevelIndex = currentPosition.round().clamp(0, levels.length - 1);

    // 绘制气泡文本
    final bubbleTextPainter = TextPainter(
      text: TextSpan(
        text: '${levels[currentLevelIndex]}级别',
        style: Get.theme.textTheme.bodyMedium?.copyWith(color: Get.isDarkMode ? Colors.black : Colors.white),
      ),
      textDirection: ui.TextDirection.ltr,
    );
    bubbleTextPainter.layout();
    bubbleTextPainter.paint(
      canvas,
      Offset(
        animX - bubbleTextPainter.width / 2,
        bubbleTop + (bubbleHeight - bubbleTextPainter.height) / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(LevelChartPainter oldDelegate) {
    return oldDelegate.selectedIndex != selectedIndex || oldDelegate.currentPosition != currentPosition;
  }
}

class TimeSelectionStage extends StatelessWidget {
  const TimeSelectionStage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GuideController>();
    final timeOptions = controller.studyTimeOptions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          '你平常希望每天抽出\n多长时间学习',
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Gap(30.whs),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: timeOptions.length,
            itemBuilder: (context, index) {
              final isSelected = controller.selectedStudyTimeIndex.value == index;
              return AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withAlpha(25) : Colors.white,
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ListTile(
                  title: Text(timeOptions[index]),
                  trailing: AnimatedOpacity(
                    duration: const Duration(milliseconds: 500),
                    opacity: isSelected ? 1.0 : 0.0,
                    child: Icon(Icons.check_circle, color: Theme.of(context).primaryColor),
                  ),
                  onTap: () {
                    controller.selectedStudyTimeIndex.value = index;
                    controller.selectedChoices['study_time'] = index;
                  },
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: () {
              // 保存选择的时间并进入下一阶段
              controller.selectTimeChoice(controller.selectedStudyTimeIndex.value);
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: const Color(0xff101828),
              foregroundColor: Colors.white,
            ),
            child: const Text('下一步'),
          ),
        ),
      ],
    );
  }
}

// 预测结果页面
class PredictionResultStage extends StatelessWidget {
  const PredictionResultStage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GuideController>();

    return Obx(() {
      final learningPlan = controller.learningPlanResp.value;

      // 如果学习计划为空，显示加载中
      if (learningPlan == null) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // 获取开始和目标级别
      final startLevel = learningPlan.startLevel;
      final targetLevel = learningPlan.targetLevel;

      // 获取结束日期
      final endDate = learningPlan.endDate ?? '';

      // 格式化日期显示
      String formattedDate = '';
      if (endDate.isNotEmpty) {
        try {
          final date = DateTime.parse(endDate);
          formattedDate = DateFormat('yyyy年M月d日').format(date);
        } catch (e) {
          // 如果日期解析失败，使用原始字符串
          formattedDate = endDate;
        }
      } else {
        // 如果没有结束日期，使用当前日期加3个月作为预估
        final now = DateTime.now();
        final targetDate = DateTime(now.year, now.month + 3, now.day);
        formattedDate = DateFormat('yyyy年M月d日').format(targetDate);
      }

      // 准备阶段数据
      final stageEndDates = <String>[];
      final stageDescriptions = <String>[];

      // 从学习计划中提取阶段信息
      for (final stage in learningPlan.stages ?? []) {
        if (stage.endDate != null && stage.endDate!.isNotEmpty) {
          try {
            final date = DateTime.parse(stage.endDate!);
            stageEndDates.add(DateFormat('MM/dd').format(date));
          } catch (e) {
            stageEndDates.add(stage.endDate!);
          }
        } else {
          stageEndDates.add('未知');
        }

        stageDescriptions.add(stage.stageDesc);
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '预测你将在$formattedDate\n达到$targetLevel的水平',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Spacer(),
          // 使用新的图表绘制器
          SizedBox(
            width: Get.width,
            height: 300,
            child: CustomPaint(
              painter: PredictionChartPainter(
                startLevel: startLevel,
                targetLevel: targetLevel,
                stageEndDates: stageEndDates,
                stageDescriptions: stageDescriptions,
              ),
            ),
          ),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: () {
                // 完成引导流程
                Get.back();
              },
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 50),
                backgroundColor: const Color(0xff101828),
                foregroundColor: Colors.white,
              ),
              child: const Text('开始学习'),
            ),
          ),
        ],
      );
    });
  }
}

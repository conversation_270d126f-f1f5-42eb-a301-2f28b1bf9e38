import 'package:get/get.dart';

import '../modules/aliyunpan/bindings/aliyunpan_binding.dart';
import '../modules/aliyunpan/views/aliyunpan_view.dart';
import '../modules/datacenter/bindings/datacenter_binding.dart';
import '../modules/datacenter/episodedata/bindings/episodedata_binding.dart';
import '../modules/datacenter/episodedata/views/episodedata_view.dart';
import '../modules/datacenter/episodelist/bindings/episodelist_binding.dart';
import '../modules/datacenter/episodelist/views/episodelist_view.dart';
import '../modules/datacenter/views/datacenter_view.dart';
import '../modules/detail/bindings/detail_binding.dart';
import '../modules/detail/views/detail_view.dart';
import '../modules/guide/bindings/guide_binding.dart';
import '../modules/guide/views/guide_view.dart';
import '../modules/history/bindings/history_binding.dart';
import '../modules/history/views/history_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/language_settings/bindings/language_settings_binding.dart';
import '../modules/language_settings/views/language_settings_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/lsdesc/bindings/lsdesc_binding.dart';
import '../modules/lsdesc/views/lsdesc_view.dart';
import '../modules/main/bindings/main_binding.dart';
import '../modules/main/views/main_view.dart';
import '../modules/mine/audio_convert/bindings/audio_convert_binding.dart';
import '../modules/mine/audio_convert/views/audio_convert_view.dart';
import '../modules/mine/bindings/mine_binding.dart';
import '../modules/mine/video_compress/bindings/video_compress_binding.dart';
import '../modules/mine/video_compress/views/video_compress_view.dart';
import '../modules/mine/views/mine_view.dart';
import '../modules/notelist/bindings/notelist_binding.dart';
import '../modules/notelist/views/notelist_view.dart';
import '../modules/orderlist/bindings/orderlist_binding.dart';
import '../modules/orderlist/views/orderlist_view.dart';
import '../modules/pay/bindings/pay_binding.dart';
import '../modules/pay/views/pay_view.dart';
import '../modules/plan_gen_detail/bindings/plan_gen_detail_binding.dart';
import '../modules/plan_gen_detail/views/plan_gen_detail_view.dart';
import '../modules/resourcelib/bindings/resourcelib_binding.dart';
import '../modules/resourcelib/series/bindings/series_binding.dart';
import '../modules/resourcelib/series/views/series_view.dart';
import '../modules/resourcelib/views/resourcelib_view.dart';
import '../modules/setting/bindings/setting_binding.dart';
import '../modules/setting/views/setting_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/subtitle/bindings/subtitle_binding.dart';
import '../modules/subtitle/subtitle_add/bindings/subtitle_add_binding.dart';
import '../modules/subtitle/subtitle_add/views/subtitle_add_view.dart';
import '../modules/subtitle/subtitle_edit/bindings/subtitle_edit_binding.dart';
import '../modules/subtitle/subtitle_edit/views/subtitle_edit_view.dart';
import '../modules/subtitle/subtitle_preview/bindings/subtitle_preview_binding.dart';
import '../modules/subtitle/subtitle_preview/views/subtitle_preview_view.dart';
import '../modules/subtitle/subtitle_search/bindings/subtitle_search_binding.dart';
import '../modules/subtitle/subtitle_search/views/subtitle_search_view.dart';
import '../modules/subtitle/views/subtitle_view.dart';
import '../modules/web/bindings/web_binding.dart';
import '../modules/web/views/web_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.HOME;
  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.MAIN,
      page: () => const MainView(),
      binding: MainBinding(),
    ),
    GetPage(
      name: _Paths.DETAIL,
      page: () => const DetailView(),
      binding: DetailBinding(),
    ),
    GetPage(
      name: _Paths.SUBTITLE,
      page: () => const SubtitleView(),
      binding: SubtitleBinding(),
      children: [
        GetPage(
          name: _Paths.SUBTITLE_ADD,
          page: () => const SubtitleAddView(),
          binding: SubtitleAddBinding(),
        ),
        GetPage(
          name: _Paths.SUBTITLE_SEARCH,
          page: () => const SubtitleSearchView(),
          binding: SubtitleSearchBinding(),
        ),
        GetPage(
          name: _Paths.SUBTITLE_PREVIEW,
          page: () => const SubtitlePreviewView(),
          binding: SubtitlePreviewBinding(),
        ),
        GetPage(
          name: _Paths.SUBTITLE_EDIT,
          page: () => const SubtitleEditView(),
          binding: SubtitleEditBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.SETTING,
      page: () => const SettingView(),
      binding: SettingBinding(),
    ),
    GetPage(
      name: _Paths.HISTORY,
      page: () => const HistoryView(),
      binding: HistoryBinding(),
    ),
    GetPage(
      name: _Paths.NOTELIST,
      page: () => const NotelistView(),
      binding: NotelistBinding(),
    ),
    GetPage(
      name: _Paths.DATACENTER,
      page: () => const DatacenterView(),
      binding: DatacenterBinding(),
      children: [
        GetPage(
          name: _Paths.EPISODEDATA,
          page: () => const EpisodedataView(),
          binding: EpisodedataBinding(),
        ),
        GetPage(
          name: _Paths.EPISODELIST,
          page: () => const EpisodelistView(),
          binding: EpisodelistBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.MINE,
      page: () => const MineView(),
      binding: MineBinding(),
      children: [
        GetPage(
          name: _Paths.AUDIO_CONVERT,
          page: () => const AudioConvertView(),
          binding: AudioConvertBinding(),
        ),
        GetPage(
          name: _Paths.VIDEO_COMPRESS,
          page: () => const VideoCompressView(),
          binding: VideoCompressBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.RESOURCELIB,
      page: () => const ResourcelibView(),
      binding: ResourcelibBinding(),
      children: [
        GetPage(
          name: _Paths.SERIES,
          page: () => const SeriesView(),
          binding: SeriesBinding(),
        ),
      ],
    ),
    GetPage(
      name: _Paths.PAY,
      page: () => const PayView(),
      binding: PayBinding(),
    ),
    GetPage(
      name: _Paths.ORDERLIST,
      page: () => const OrderlistView(),
      binding: OrderlistBinding(),
    ),
    GetPage(
      name: _Paths.WEB,
      page: () => const WebView(),
      binding: WebBinding(),
    ),
    GetPage(
      name: _Paths.ALIYUNPAN,
      page: () => const AliyunpanView(),
      binding: AliyunpanBinding(),
    ),
    GetPage(
      name: _Paths.GUIDE,
      page: () => const GuideView(),
      binding: GuideBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: _Paths.LSDESC,
      page: () => const LsdescView(),
      binding: LsdescBinding(),
    ),
    GetPage(
      name: _Paths.PLAN_GEN_DETAIL,
      page: () => const PlanGenDetailView(),
      binding: PlanGenDetailBinding(),
    ),
    GetPage(
      name: _Paths.LANGUAGE_SETTINGS,
      page: () => const LanguageSettingsView(),
      binding: LanguageSettingsBinding(),
    ),
  ];
}

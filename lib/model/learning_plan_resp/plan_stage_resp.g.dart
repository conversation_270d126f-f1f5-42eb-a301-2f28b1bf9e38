// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_stage_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanStageResp _$PlanStageRespFromJson(Map<String, dynamic> json) =>
    PlanStageResp(
      id: (json['id'] as num).toInt(),
      stageDesc: json['stageDesc'] as String,
      objective: json['objective'] as String,
      task: json['task'] as String,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
      resources: (json['resources'] as List<dynamic>)
          .map((e) => PlanResourceResp.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PlanStageRespToJson(PlanStageResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'stageDesc': instance.stageDesc,
      'objective': instance.objective,
      'task': instance.task,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'resources': instance.resources,
    };

import 'package:json_annotation/json_annotation.dart';
import 'plan_resource_resp.dart';

part 'plan_stage_resp.g.dart';

@JsonSerializable()
class PlanStageResp {
  final int id;
  final String stageDesc; // 阶段描述，如 A0-1, A1-2 等
  final String objective;
  final String task;
  final String? startDate;
  final String? endDate;
  final List<PlanResourceResp> resources;

  PlanStageResp({
    required this.id,
    required this.stageDesc,
    required this.objective,
    required this.task,
    this.startDate,
    this.endDate,
    required this.resources,
  });

  factory PlanStageResp.fromJson(Map<String, dynamic> json) => _$PlanStageRespFromJson(json);

  Map<String, dynamic> toJson() => _$PlanStageRespToJson(this);
}

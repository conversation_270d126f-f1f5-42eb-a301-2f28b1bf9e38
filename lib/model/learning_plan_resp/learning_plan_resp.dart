import 'package:json_annotation/json_annotation.dart';
import 'plan_stage_resp.dart';

part 'learning_plan_resp.g.dart';

@JsonSerializable()
class LearningPlanResp {
  final int id;
  final String startLevel;
  final String targetLevel;
  final String startDate;
  final String? endDate;
  final int status;
  final List<PlanStageResp>? stages;
  final String createdAt;

  LearningPlanResp({
    required this.id,
    required this.startLevel,
    required this.targetLevel,
    required this.startDate,
    this.endDate,
    required this.status,
    required this.stages,
    required this.createdAt,
  });

  factory LearningPlanResp.fromJson(Map<String, dynamic> json) => _$LearningPlanRespFromJson(json);

  Map<String, dynamic> toJson() => _$LearningPlanRespToJson(this);
}

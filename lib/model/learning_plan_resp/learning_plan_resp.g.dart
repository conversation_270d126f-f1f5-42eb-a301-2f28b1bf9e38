// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_plan_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LearningPlanResp _$LearningPlanRespFromJson(Map<String, dynamic> json) =>
    LearningPlanResp(
      id: (json['id'] as num).toInt(),
      startLevel: json['startLevel'] as String,
      targetLevel: json['targetLevel'] as String,
      startDate: json['startDate'] as String,
      endDate: json['endDate'] as String?,
      status: (json['status'] as num).toInt(),
      stages: (json['stages'] as List<dynamic>?)
          ?.map((e) => PlanStageResp.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] as String,
    );

Map<String, dynamic> _$LearningPlanRespToJson(LearningPlanResp instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startLevel': instance.startLevel,
      'targetLevel': instance.targetLevel,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'status': instance.status,
      'stages': instance.stages,
      'createdAt': instance.createdAt,
    };

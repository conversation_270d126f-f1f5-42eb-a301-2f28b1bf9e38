import 'package:json_annotation/json_annotation.dart';

part 'video_time_interval.g.dart';

@JsonSerializable()
class VideoTimeInterval {
  int? start;
  int? end;

  VideoTimeInterval({
    this.start,
    this.end,
  });

  factory VideoTimeInterval.fromJson(Map<String, dynamic> json) {
    return _$VideoTimeIntervalFromJson(json);
  }

  Map<String, dynamic> toJson() => _$VideoTimeIntervalToJson(this);
}

import 'package:dio/dio.dart' hide Headers;
import 'package:lsenglish/model/config/user_config.dart';
import 'package:lsenglish/model/data_center_home_resp/data_center_home_resp.dart';
import 'package:lsenglish/model/english_dict_model/english_dict_model.dart';
import 'package:lsenglish/model/episode_ls_data_resp/episode_ls_data_resp.dart';
import 'package:lsenglish/model/learning_plan_resp/learning_plan_resp.dart';
import 'package:lsenglish/model/local_detail_resp/resource_detail_resp.dart';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/model/local_sentence_collect_resp/local_sentence_collect_resp.dart';
import 'package:lsenglish/model/order_item_resp%20copy/order_item_resp.dart';
import 'package:lsenglish/model/speech_evaluation_resp.dart';
import 'package:lsenglish/model/setting/langs.dart';
import 'package:lsenglish/model/user_login_resp/user.dart';
import 'package:lsenglish/model/user_login_resp/user_login_resp.dart';
import 'package:lsenglish/model/vip/user_vip_info_resp/user_vip_info_resp.dart';
import 'package:lsenglish/model/vip/vip_product_resp/vip_product_resp.dart';
import 'package:lsenglish/model/watch_history_resp/watch_history_resp.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';
import '../model/data_center_chart_resp/data_center_chart_resp.dart';
import '../model/data_center_home_resp/episode.dart';
import '../model/resource_lib_home_resp/resource_lib_home_item_resp.dart';
import '../model/resource_lib_home_resp/resource_lib_home_resp.dart';
import '../model/series_resp/series_detail_resp.dart';
import '../model/vip/vip_order_create_resp/vip_order_create_resp.dart';
import 'api_result.dart';
part 'client.g.dart';

@RestApi(baseUrl: '/api/v1/')
abstract class RestClient {
  factory RestClient(Dio dio) = _RestClient;

  @POST('upload')
  @MultiPart()
  @Headers(<String, dynamic>{
    "accept": "*/*",
    "Content-Type": "multipart/form-data",
  })
  Future<ApiResult<List<String>?>?> uploadFiles({
    @Part() required List<MultipartFile> files,
  });

  @GET('https://dict.youdao.com/jsonapi')
  Future<EnglishDictModel> getEnglishDict(
    @Query('jsonversion') String jsonversion,
    @Query('client') String client,
    @Query('q') String q,
    @Query('network') String network,
    @Query('xmlVersion') String xmlVersion,
    @Query('dicts') String dicts,
  );

  @POST('user/login')
  Future<ApiResult<UserLoginResp>> login(@Body() Map<String, dynamic> map);

  @GET('user')
  Future<ApiResult<User>> userInfo();

  @PUT('user')
  Future<ApiResult<User>> updateUserInfo(@Body() Map<String, dynamic> map);

  @POST('user/loginByApple')
  Future<ApiResult<UserLoginResp>> loginByApple(@Body() Map<String, dynamic> map);

  @GET('user/config')
  Future<ApiResult<UserConfig>> config();

  @GET('langs')
  Future<ApiResult<LangsModel>> getLangs();

  @PUT('user/playerConfig')
  Future<ApiResult<UserConfig>> updatePlayerConfig(@Body() Map<String, dynamic> map);

  @POST('video/detail')
  Future<ApiResult<ResourceDetailResp>> getVideoDetail(@Body() Map<String, dynamic> map);

  @POST('video/updateCutsomSubtitle')
  Future<ApiResult<ResourceDetailResp>> updateCutsomSubtitle(@Body() Map<String, dynamic> map);

  @PUT('video/updateVideoDetail')
  Future<ApiResult> updateVideoDetail(@Body() Map<String, dynamic> map);

  @POST('collect/sentence')
  Future<ApiResult> addLocalSencenceCollect(@Body() Map<String, dynamic> map);

  @DELETE('collect/sentence')
  Future<ApiResult> removeLocalSencenceCollect(@Body() Map<String, dynamic> map);

  @GET('collect/sentence')
  Future<ApiResult<LocalSentenceCollectResp>> localsentence(@Query('subtitleId') String subtitleId);

  @POST('note')
  Future<ApiResult> addNote(@Body() Map<String, dynamic> map);

  @GET('notes')
  Future<ApiResult<List<NoteModel>>> getNoteList(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize,
    @Query('resourceId') String resourceId,
    @Query('resourceType') int resourceType,
  );

  @GET('note')
  Future<ApiResult<NoteModel>> localNote(@Query('id') String id);

  @DELETE('note')
  Future<ApiResult> deleteLocalNote(@Body() Map<String, dynamic> map);

  // 语音评测相关接口
  @GET('speech/evaluations')
  Future<ApiResult<List<SpeechEvaluationResp>>> getSpeechEvaluations(
    @Query('resourceId') String resourceId,
    @Query('resourceType') int resourceType,
  );

  @POST('speech/evaluations')
  Future<ApiResult<SpeechEvaluationResp>> createSpeechEvaluation(@Body() Map<String, dynamic> map);

  @PUT('speech/evaluations')
  Future<ApiResult<SpeechEvaluationResp>> updateSpeechEvaluation(@Body() Map<String, dynamic> map);

  @PUT('speech/evaluations/batch/audio')
  Future<ApiResult> batchUpdateSpeechEvaluationAudioUrl(@Body() Map<String, dynamic> map);

  @GET('user/watchHistory')
  Future<ApiResult<List<WatchHistoryResp>>> watchHistorys(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize,
  );

  @DELETE('user/watchHistory')
  Future<ApiResult> deleteWatchHistory(@Body() Map<String, dynamic> map);

  @POST('video/changeResourceName')
  Future<ApiResult> changeResourceName(@Body() Map<String, dynamic> map);

  @POST('dataEpisode')
  Future<ApiResult> addDataEpisode(@Body() Map<String, dynamic> map);

  @PUT('dataEpisode/modLsTime')
  Future<ApiResult> modDataEpisode(@Body() Map<String, dynamic> map);

  @GET('dataEpisodeHome')
  Future<ApiResult<DataCenterHomeResp>> dataEpisodeHome(
    @Query('type') int type,
    @Query('startTime') int startTime,
    @Query('endTime') int endTime,
  );
  @GET('dataEpisodeList')
  Future<ApiResult<List<Episode>>> dataEpisodeList();
  @GET('dataEpisodeChart')
  Future<ApiResult<DataCenterChartResp>> dataEpisodeChart(
    @Query('type') int type,
    @Query('startTime') int startTime,
    @Query('endTime') int endTime,
  );

  @GET('dataEpisodeLsData')
  Future<ApiResult<EpisodeLsDataResp>> dataEpisodeLsData(
    @Query('resourceId') String resourceId,
    @Query('resourceType') int resourceType,
  );

  @GET('resourceHome')
  Future<ApiResult<ResourceLibHomeResp>> resourceHome();

  @POST('resourceHomeItems')
  Future<ApiResult<List<ResourceLibHomeItemResp>>> resourceHomeItems(@Body() Map<String, dynamic> map);

  @GET('series')
  Future<ApiResult<SeriesDetailResp>> getResourceBySeriesId(@Query('id') String id);

  @GET('vip/products')
  Future<ApiResult<List<VipProductResp>>> getVipProducts();

  @POST('vip/createOrder')
  Future<ApiResult<VipCreateOrderResp>> createOrder(@Body() Map<String, dynamic> map);

  @GET('vip/orderList')
  Future<ApiResult<List<OrderItemResp>>> getOrderList();

  @GET('vip/info')
  Future<ApiResult<UserVipInfoResp>> vipInfo();

  @POST('vip/exchangeCode')
  Future<void> exchangeCode(@Body() Map<String, dynamic> data);

  @POST('plan/generate')
  Future<HttpResponse> generatePlan(@Body() Map<String, dynamic> data);

  @GET('plan')
  Future<ApiResult<LearningPlanResp?>> currentPlan();

  @POST('feedback')
  Future<HttpResponse> addFeedback(@Body() Map<String, dynamic> data);
}

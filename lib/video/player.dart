import 'dart:async';
import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/log.dart' as log;
import 'package:lsenglish/utils/routes.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/utils/subtitle/src/utils/subtitle_controller.dart';
import 'package:lsenglish/utils/util.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:media_kit/media_kit.dart';
import 'package:path/path.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'audio_handler.dart';

abstract class IPlayer {
  static MyAudioHandler? audioHandler;
  void init();
  Future<void> play();
  Future<void> pause();
  Future<void> mute();
  Future<void> unmute();
  void stop();
  Future open(String path);
  bool isPlaying();
  Future<void> loadSubtitles(String subtitlePath, {String nativeSubtitlePath});
  Future<void> seekBySubtitleIndex(int index);
  void preSubtitle();
  void nextSubtitle();
  void switchSubtitleMode();
  Subtitle? refreshSubtitle(Duration duration, {bool updateSubtitleIndex = true});
  void delaySubtitle(int milliseconds);
  void enableOnlyPlayLines();
  void disableOnlyPlayLines();
  void switchOnlyPlayLines();
  void enableOpenSingleRepeat();
  void disableOpenSingleRepeat();
  void switchOpenSingleRepeat();
  Future<void> switchSpeed();
  void playOrPause();
  Future<void> seek(Duration duration);
  Future<void> onPosition(Duration duration);
  void toggleFullscreen();
  void showAudioService();
  void dismissAudioService();
  Future<Uint8List?> screenshot();
  Future<String?> screenshot2File();
  void destory();
  Future<void> resetLsModeIndex({bool needPlay = true});
  void resetOnHorizontalDragEndTime();
  Future<void> onVideoSeekChangeEnd();
  Future<void> seekByHistory(int positionInMilliseconds);
  bool currentDurationIsMatchCurrentSubtitleEndInLsMode({int index = -1});
  Future<void> enterNativeFullscreen();
  Future<void> exitNativeFullscreen();
  Future<void> unlockOrientation();
  void onVideoLoadFinish();
  bool isUnlockingOrientation = false;
  //是否已经完成了根据历史进度来跳转
  bool finishSeekByHistory = false;
  RxInt seekByHistoryIndex = 0.obs;
  RxBool pausedInLsMode = false.obs;
  RxBool openLsMode = true.obs;
  RxList<Subtitle> subtitles = <Subtitle>[].obs;
  RxInt currentSubtitleIndex = 0.obs;
  //发生了跳过逻辑时，currentSubtitleIndex被修改，seekSkipSubtitleIndex同步更新
  RxInt seekSkipSubtitleIndex = 0.obs;
  RxInt currentSubtitleMode = SubtitleMode.bilingual.index.obs;
  int currentPositionInMilliseconds = 0;
  RxDouble currentSpeed = 1.0.obs;
  //仅播放台词
  RxBool onlyPlayLines = false.obs;
  //是否打开单句循环播放
  RxBool openSingleRepeat = false.obs;
  //判断当前的视频时间中，是不是没有字幕
  RxBool subtitleNone = false.obs;
  SubtitleController? subtitleController;
  RxBool playing = false.obs;
  //LS模式下是否自动播放录音
  bool autoPlayRecordInLsMode = true;
  //LS模式下是否自动开始录音
  bool autoStartRecordInLsMode = true;
  final _playerConfig = Config().playerConfig;
  int _currentLsModeIndex = -1;
  String subtitlePath = "";
  String _videoPath = "";
  int _currentSingleRepeatNum = 0;
  int _currentSingleRepeatIndex = -1;
  int onHorizontalDragEndTime = 0;
  List<int> skipList = [];
  //第一次进入 服务端获取到的进度
  int positionInit = 0;
  bool skipFindReverse = false;
  final Completer<void> _videoLoadFinish = Completer<void>();

  //所有的变量都需要重置
  IPlayer() {
    reset();
  }
  void logger(String msg) {
    // log.logger("PlayerLogger $msg");
  }

  /// 检查LS模式下的状态一致性
  void _checkLsModeStateConsistency(String context) {
    if (openLsMode.value && subtitles.isNotEmpty) {
      var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
      var subtitle = subtitleController?.durationSearch(currentDuration);
      var expectedIndex = subtitle?.subtitleIndex ?? -1;

      if (_currentLsModeIndex != -1 && expectedIndex != -1 && _currentLsModeIndex != expectedIndex) {
        logger("[$context] LS mode state inconsistency detected:");
        logger("  _currentLsModeIndex: $_currentLsModeIndex");
        logger("  expectedIndex: $expectedIndex");
        logger("  currentSubtitleIndex: ${currentSubtitleIndex.value}");
        logger("  currentPosition: ${currentPositionInMilliseconds}ms");
      }
    }
  }

  void reset() {
    logger("---------reset---------");
    logger("Resetting all player states:");

    subtitles = <Subtitle>[].obs;
    currentSubtitleIndex = 0.obs;
    seekSkipSubtitleIndex = 0.obs;
    currentSubtitleMode = SubtitleMode.bilingual.index.obs;
    currentPositionInMilliseconds = 0;
    currentSpeed = 1.0.obs;
    onlyPlayLines = false.obs;
    subtitleNone = false.obs;
    openSingleRepeat = false.obs;
    openLsMode = true.obs;
    finishSeekByHistory = false;
    _currentLsModeIndex = -1;
    subtitlePath = "";
    _videoPath = "";
    _currentSingleRepeatNum = 0;
    _currentSingleRepeatIndex = -1;
    playing = false.obs;
    autoPlayRecordInLsMode = true;
    autoStartRecordInLsMode = true;
    positionInit = -1;
    skipFindReverse = false;
    pausedInLsMode = false.obs;
    seekByHistoryIndex = 0.obs;

    logger("Reset completed - all states initialized");
  }

  static Future<void> ensureInitialized() async {
    void handleReceiveSharingIntent(List<SharedMediaFile> fileList) async {
      var filePath = "";
      for (var i = 0; i < fileList.length; i++) {
        var tempPath = fileList[i].path;
        var videoPath = await FileUtils().findVideoFilePath((await FileUtils().getSaveDir()).path, basenameWithoutExtension(fileList[i].path));
        log.logger("handleReceiveSharingIntent findVideoFilePath videoPath=$videoPath");
        var needMoveToDocuments = videoPath == null || videoPath == "";
        if (needMoveToDocuments) {
          tempPath = await FileUtils().moveFileToDocuments(fileList[i].path);
        }
        if (i == 0) {
          filePath = tempPath;
        }
      }
      RoutesUtil().goDetailByPath(filePath);
    }

    MediaKit.ensureInitialized();
    //TODO 下拉状态栏就出问题
    // IPlayer.audioHandler = await AudioService.init(
    //   builder: () => MyAudioHandler(),
    //   config: const AudioServiceConfig(
    //     androidNotificationChannelId: 'com.mikaelzero.lsenglish',
    //     androidNotificationChannelName: 'Music playback',
    //   ),
    // );
    ReceiveSharingIntent.instance.getMediaStream().listen((value) {
      log.logger("----- ReceiveSharingIntent ------- $value");
      handleReceiveSharingIntent(value);
    }, onError: (err) {
      log.logger("getIntentDataStream error: $err");
    });
    ReceiveSharingIntent.instance.getInitialMedia().then((value) {
      log.logger("----- ReceiveSharingIntent getInitialMedia------- $value");
      handleReceiveSharingIntent(value);
      ReceiveSharingIntent.instance.reset();
    }, onError: (err) {
      log.logger("getIntentDataStream222 error: $err");
    });
  }
}

abstract class BasePlayer extends IPlayer {
  @override
  void init() async {
    _currentSingleRepeatNum = _playerConfig.singleRepeatCount;
  }

  @override
  Future open(String path) async {
    logger("open() called with path: $path");
    _videoPath = path;
    IPlayer.audioHandler?.initPlayer();
  }

  Future<String> getSubtitleByVideo(String videoPath) async {
    var subtitleName = basenameWithoutExtension(videoPath);
    logger("getSubtitleByVideo subtitleName = $subtitleName");
    var subtitlePath = "${(await FileUtils().getSaveSubtitleDir()).path}/$subtitleName.srt";
    logger("getSubtitleByVideo subtitlePath = $subtitlePath");
    var result = await FFmpegUtils().extractSubtitles(videoPath, subtitlePath);
    logger("getSubtitleByVideo result = $result");
    if (result) {
      SPUtil().saveHistory(videoPath, subtitleLocalPath: subtitlePath);
      return subtitlePath;
    }
    return "";
  }

  @override
  Future<void> onPosition(Duration duration) async {
    logger("onPosition() called with duration: $duration, finishSeekByHistory: $finishSeekByHistory");
    currentPositionInMilliseconds = duration.inMilliseconds;
    //应该有个逻辑  因为会有跳转到指定的位置的逻辑 所以在跳转到指定的位置之前 onPosition 里的逻辑不应该被调用
    //选择了字幕之后 也需要调用 seekHistory 否则会导致这里一直为false
    if (!finishSeekByHistory) {
      logger("onPosition() finishSeekByHistory is false, skipping logic. positionInit: $positionInit");
      return;
    }
    IPlayer.audioHandler?.ready();
    if (subtitles.isEmpty) {
      logger("onPosition() subtitles is empty, returning");
      return;
    }

    bool seeked = await _seekNextIndexWhenNeedSkip();
    if (seeked) {
      logger("onPosition() seeked to next index, returning");
      return;
    }
    if (openLsMode.value) {
      logger("onPosition() handling LS mode");
      _checkLsModeStateConsistency("onPosition");
      handleLsMode(duration);
    } else {
      //暂定 如果是ls模式 不应该触发这些逻辑 因为都是一句一句播放
      if (onlyPlayLines.value) {
        logger("onPosition() handling only play lines");
        await handleOnlyPlayLines(duration);
      }
      if (openSingleRepeat.value) {
        logger("onPosition() handling single repeat");
        await handleOpenSingleRepeat(duration);
      } else {
        logger("onPosition() refreshing subtitle");
        refreshSubtitle(duration);
      }
    }
  }

  //如果在LS模式下  播放完当前的句子后  暂停播放
  //如果当前进度没有字幕信息  则不进行暂停
  //如果是用户拖动进度条的话

  Future<void> handleLsMode(Duration duration) async {
    logger(
        "handleLsMode() called with duration: $duration, _currentLsModeIndex: $_currentLsModeIndex, currentSubtitleIndex: ${currentSubtitleIndex.value}");

    //不为-1 代表已经识别到了字幕并且正在播放这个字幕
    if (_currentLsModeIndex != -1) {
      // 确保索引状态一致性
      if (currentSubtitleIndex.value != _currentLsModeIndex) {
        logger("handleLsMode() syncing currentSubtitleIndex from ${currentSubtitleIndex.value} to $_currentLsModeIndex");
        currentSubtitleIndex.value = _currentLsModeIndex;
      }
      await handlePauseInLsMode(duration);
      return;
    }

    //不调用refreshSubtitle就无法确定当前的
    var subtitle = refreshSubtitle(duration);
    logger("handleLsMode() subtitle: ${subtitle?.subtitleIndex} ${subtitle?.start} ${subtitle?.end}");
    if (subtitle == null) {
      logger("handleLsMode() subtitle is null, seeking next subtitle");
      await seekExistNextSubtitleOrPreSubtitle(duration);
      return;
    }

    if (_currentLsModeIndex == -1) {
      _currentLsModeIndex = subtitle.subtitleIndex;
      logger("handleLsMode() set _currentLsModeIndex to: $_currentLsModeIndex");
      // 确保索引状态一致性
      if (currentSubtitleIndex.value != _currentLsModeIndex) {
        logger("handleLsMode() syncing currentSubtitleIndex to $_currentLsModeIndex");
        currentSubtitleIndex.value = _currentLsModeIndex;
      }
    }
    await handlePauseInLsMode(duration);
  }

  Future<void> handlePauseInLsMode(Duration duration) async {
    var currentSubtitleEnd = subtitles[_currentLsModeIndex].end.inMilliseconds;
    // logger("handlePauseInLsMode() currentSubtitleEnd: $currentSubtitleEnd, duration: $duration");
    if (duration.inMilliseconds >= currentSubtitleEnd) {
      // logger("handlePauseInLsMode() pausing at index: $_currentLsModeIndex");
      await pause();
      pausedInLsMode.value = true;
      pausedInLsMode.refresh();
    }
  }

  Future<void> seekExistNextSubtitleOrPreSubtitle(Duration duration) async {
    var subtitle = subtitleController?.findClosestSubtitleForward(duration);
    if (subtitle != null) {
      await seek(subtitle.start);
    }
  }

  @override
  bool currentDurationIsMatchCurrentSubtitleEndInLsMode({int index = -1}) {
    if (index == -1) {
      index = _currentLsModeIndex;
    }
    if (index == -1) {
      return false;
    }
    var currentSubtitleEnd = subtitles[index].end.inMilliseconds;
    if (currentPositionInMilliseconds >= currentSubtitleEnd || currentSubtitleEnd - currentPositionInMilliseconds < 10) {
      return true;
    }
    return false;
  }

  @override
  Future<void> resetLsModeIndex({bool needPlay = true}) async {
    logger("resetLsModeIndex called, needPlay: $needPlay, current _currentLsModeIndex: $_currentLsModeIndex");
    _currentLsModeIndex = -1;
    pausedInLsMode.value = false;

    // 在LS模式下，确保状态一致性
    if (openLsMode.value && subtitles.isNotEmpty) {
      var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
      var subtitle = subtitleController?.durationSearch(currentDuration);
      if (subtitle != null && subtitle.subtitleIndex != currentSubtitleIndex.value) {
        logger("resetLsModeIndex updating currentSubtitleIndex from ${currentSubtitleIndex.value} to ${subtitle.subtitleIndex}");
        currentSubtitleIndex.value = subtitle.subtitleIndex;
      }
    }

    if (needPlay) {
      logger("resetLsModeIndex calling play()");
      play();
    }
  }

  @override
  Future<void> onVideoSeekChangeEnd() async {
    logger("onVideoSeekChangeEnd called");
    //结束的时候
    //1. 需要重置_currentLsModeIndex
    //2. 如果结束时的时间获取到的subtitle为空，那就直接获取到下一句的进度 然后seek 在onPosition里处理
    //3. 需要判断一个时间差 拖动结束后 如果打开了自动录制 如果触发结束录制的时间和拖动结束的时间差距过小 则不触发自动录制
    onHorizontalDragEndTime = DateTime.now().millisecondsSinceEpoch;

    // 重置LS模式状态，但不立即播放，让onPosition处理后续逻辑
    logger("onVideoSeekChangeEnd resetting LS mode index");
    _currentLsModeIndex = -1;
    pausedInLsMode.value = false;

    // 如果在LS模式下，需要根据当前位置更新字幕索引
    if (openLsMode.value && subtitles.isNotEmpty) {
      var currentDuration = Duration(milliseconds: currentPositionInMilliseconds);
      var subtitle = subtitleController?.durationSearch(currentDuration);
      if (subtitle != null) {
        logger("onVideoSeekChangeEnd found subtitle at index ${subtitle.subtitleIndex}");
        currentSubtitleIndex.value = subtitle.subtitleIndex;
      }
    }
  }

  @override
  void resetOnHorizontalDragEndTime() {
    onHorizontalDragEndTime = 0;
  }

  Future<void> handleOnlyPlayLines(Duration duration) async {
    logger("handleOnlyPlayLines() called with duration: $duration");
    if (openSingleRepeat.value) {
      if (_playerConfig.singleRepeatCount == 0) {
        logger("handleOnlyPlayLines() singleRepeatCount is 0, returning");
        return;
      }
      if (_currentSingleRepeatNum > 0) {
        logger("handleOnlyPlayLines() waiting for single repeat to finish");
        return;
      }
    }
    if (currentSubtitleIndex.value + 1 >= subtitles.length) {
      logger("handleOnlyPlayLines() seeking to next subtitle index: ${currentSubtitleIndex.value + 1}");
      await seekBySubtitleIndex(currentSubtitleIndex.value + 1);
    } else {
      var nextSubtitleStart = subtitles[currentSubtitleIndex.value + 1].start.inMilliseconds;
      var currentSubtitleEnd = subtitles[currentSubtitleIndex.value].end.inMilliseconds;
      logger("handleOnlyPlayLines() nextSubtitleStart: $nextSubtitleStart, currentSubtitleEnd: $currentSubtitleEnd");
      //当当前这句和下一句之间的时间差距大于1.5秒的时候，才进行跳转，否则直接让视频播放，更加顺畅一些
      if (nextSubtitleStart - currentSubtitleEnd >= 1500) {
        logger("handleOnlyPlayLines() seeking to next subtitle due to time gap");
        await seekBySubtitleIndex(currentSubtitleIndex.value + 1);
      }
    }
  }

  Future<void> handleOpenSingleRepeat(Duration duration) async {
    logger("handleOpenSingleRepeat() called with duration: $duration");
    var subtitleEnd = subtitles[currentSubtitleIndex.value].end.inMilliseconds;

    if (_currentSingleRepeatIndex != currentSubtitleIndex.value) {
      logger("handleOpenSingleRepeat() resetting repeat count to: ${_playerConfig.singleRepeatCount}");
      _currentSingleRepeatNum = _playerConfig.singleRepeatCount;
    }
    if (currentPositionInMilliseconds >= subtitleEnd) {
      logger("handleOpenSingleRepeat() reached subtitle end, current repeat count: $_currentSingleRepeatNum");
      //_playerConfig.singleRepeatCount为0 代表无限次数
      if (_playerConfig.singleRepeatCount == 0) {
        logger("handleOpenSingleRepeat() infinite repeat, seeking to current subtitle");
        await seekBySubtitleIndex(currentSubtitleIndex.value);
      } else {
        if (_currentSingleRepeatNum > 0) {
          // 如果还有剩余次数，继续重复当前字幕
          logger("handleOpenSingleRepeat() repeating current subtitle, remaining count: $_currentSingleRepeatNum");
          _currentSingleRepeatIndex = currentSubtitleIndex.value;
          await seekBySubtitleIndex(currentSubtitleIndex.value);
          _currentSingleRepeatNum--;
        }
        if (_currentSingleRepeatNum <= 0) {
          logger("handleOpenSingleRepeat() repeat finished, refreshing subtitle");
          refreshSubtitle(duration);
        }
      }
    } else {
      logger("handleOpenSingleRepeat() refreshing subtitle without updating index");
      refreshSubtitle(duration, updateSubtitleIndex: false);
    }
  }

  @override
  Future<void> seekByHistory(int positionInMilliseconds) async {
    logger("seekByHistory  positionInMilliseconds = $positionInMilliseconds");
    //确保只被调用一次
    if (finishSeekByHistory) {
      logger("seekByHistory already finished, returning");
      return;
    }

    // 标记开始历史跳转，防止onPosition逻辑干扰
    logger("seekByHistory starting, setting finishSeekByHistory = false");

    //如果是历史记录跳转的话 应该定位到当前台词的start时间
    logger("seekByHistory subtitles size = ${subtitles.length} positionInMilliseconds=$positionInMilliseconds");
    if (subtitles.isEmpty) {
      currentSubtitleIndex.value = 0;
      logger("seekByHistory subtitle empty positionInMilliseconds = $positionInMilliseconds");
      await seek(Duration(milliseconds: positionInMilliseconds));
      // 重置LS模式相关状态
      _currentLsModeIndex = -1;
      finishSeekByHistory = true;
      seekByHistoryIndex.value = currentSubtitleIndex.value;
      logger("seekByHistory completed for empty subtitles, finishSeekByHistory = true");
      return;
    }

    var subtitle = subtitleController?.durationSearch(Duration(milliseconds: positionInMilliseconds));
    if (subtitle != null) {
      currentSubtitleIndex.value = subtitle.subtitleIndex;
      logger("seekByHistory has subtitle currentSubtitleIndex = ${currentSubtitleIndex.value} subtitle.start=${subtitle.start}");
      await seek(subtitle.start);
      // 同步LS模式索引
      _currentLsModeIndex = currentSubtitleIndex.value;
      finishSeekByHistory = true;
      seekByHistoryIndex.value = currentSubtitleIndex.value;
      logger("seekByHistory completed with subtitle, finishSeekByHistory = true, _currentLsModeIndex = $_currentLsModeIndex");
      return;
    }

    // 处理跳过逻辑
    await _seekNextIndexWhenNeedSkip();
    logger("seekByHistory default positionInMilliseconds = $positionInMilliseconds");
    await seek(Duration(milliseconds: positionInMilliseconds));
    // 重置LS模式相关状态
    _currentLsModeIndex = -1;
    finishSeekByHistory = true;
    seekByHistoryIndex.value = currentSubtitleIndex.value;
    logger("seekByHistory completed with default seek, finishSeekByHistory = true");
    return;
  }

  @override
  Future<void> loadSubtitles(String subtitlePath, {String nativeSubtitlePath = ""}) async {
    if (subtitlePath.isNotEmpty) {
      this.subtitlePath = subtitlePath;
      // var data = nativeSubtitlePath.isEmpty
      //     ? (await compute(loadSubtitlesInternal, subtitlePath))
      //     : (await compute(loadMultiSubtitlesInternal, Pair(subtitlePath, nativeSubtitlePath)));

      var data = nativeSubtitlePath.isNotEmpty
          ? await loadMultiSubtitlesInternal(Pair(subtitlePath, nativeSubtitlePath))
          : await loadSubtitlesInternal(subtitlePath);
      subtitleController = data.item1;
      subtitles.assignAll(data.item2);
      if (currentSubtitleIndex.value >= subtitles.length) {
        currentSubtitleIndex.value = subtitles.length - 1;
        _currentLsModeIndex = currentSubtitleIndex.value;
      }
    }
    logger("loadSubtitles finishSeekByHistory = $finishSeekByHistory subtitles size = ${subtitles.length}");
    if (!finishSeekByHistory && positionInit > 0) {
      logger("loadSubtitles await _videoLoadFinish");
      await Future.wait([
        _videoLoadFinish.future,
      ]);
      logger("loadSubtitles seekByHistory = $positionInit");
      await seekByHistory(positionInit);
      logger("loadSubtitles play after seekByHistory");
      play();
    } else {
      // 如果没有历史进度或已经完成历史跳转，标记为完成
      if (!finishSeekByHistory) {
        logger("loadSubtitles no history position, marking finishSeekByHistory = true");
        finishSeekByHistory = true;
        seekByHistoryIndex.value = currentSubtitleIndex.value;
      }
      //加载了字幕之后 需要根据当前的时间显示对应的字幕
      logger("loadSubtitles calling onPosition with current position");
      await onPosition(Duration(milliseconds: currentPositionInMilliseconds));
    }
  }

  @override
  void onVideoLoadFinish() {
    if (_videoLoadFinish.isCompleted) {
      return;
    }
    _videoLoadFinish.complete();
  }

  @override
  void preSubtitle() async {
    currentSubtitleIndex.value = currentSubtitleIndex.value - 1;
    await seekBySubtitleIndex(currentSubtitleIndex.value);
  }

  @override
  void nextSubtitle() async {
    currentSubtitleIndex.value = currentSubtitleIndex.value + 1;
    await seekBySubtitleIndex(currentSubtitleIndex.value);
  }

  @override
  Future<void> seekBySubtitleIndex(int index) async {
    logger("seekBySubtitleIndex() called with index: $index");
    if (subtitles.isEmpty) {
      logger("seekBySubtitleIndex() subtitles is empty, returning");
      return;
    }

    var newIndex = index;
    if (newIndex < 0) {
      logger("seekBySubtitleIndex() adjusting index from $newIndex to 0");
      newIndex = 0;
    }
    if (newIndex >= subtitles.length) {
      logger("seekBySubtitleIndex() adjusting index from $newIndex to ${subtitles.length - 1}");
      newIndex = subtitles.length - 1;
    }

    // 更新索引状态
    currentSubtitleIndex.value = newIndex;
    var duration = subtitles[newIndex].start;
    logger("seekBySubtitleIndex() seeking to ${subtitles[newIndex].start} - ${subtitles[newIndex].end}, newIndex: $newIndex");

    // 在LS模式下，重置相关状态
    if (openLsMode.value) {
      logger("seekBySubtitleIndex() in LS mode, resetting states");
      _currentLsModeIndex = -1; // 重置为-1，让handleLsMode重新识别
      pausedInLsMode.value = false;
    } else {
      // 非LS模式下，直接同步索引
      _currentLsModeIndex = newIndex;
    }

    await seek(duration);
    _checkLsModeStateConsistency("seekBySubtitleIndex");
  }

  @override
  Subtitle? refreshSubtitle(Duration duration, {bool updateSubtitleIndex = true}) {
    var subtitle = subtitleController?.durationSearch(duration);
    subtitleNone.value = subtitle == null;
    if (subtitle != null && subtitle.subtitleIndex >= 0) {
      //如果打开了openSingleRepeat，那么index的不做处理，否则会导致错乱
      if (currentSubtitleIndex.value != subtitle.subtitleIndex && updateSubtitleIndex) {
        currentSubtitleIndex.value = subtitle.subtitleIndex;
      }
    }
    return subtitle;
  }

  /////不相等说明需要调整到新的为止 需要调用的场景:
  ///1. 根据历史存的进度跳转后
  ///2. 用户主动滑动了page
  ///3. 用户滑动了进度条
  Future<bool> _seekNextIndexWhenNeedSkip() async {
    var skip2Index = _getNextSkipIndex(currentSubtitleIndex.value, reverse: skipFindReverse);
    if (skip2Index == -1) {
      await seekBySubtitleIndex(currentSubtitleIndex.value);
      seekSkipSubtitleIndex.value = currentSubtitleIndex.value;
      return true;
    }
    if (skip2Index == currentSubtitleIndex.value) {
      return false;
    }
    seekSkipSubtitleIndex.value = skip2Index;
    seekSkipSubtitleIndex.refresh();
    await seekBySubtitleIndex(skip2Index);
    return true;
  }

  //reverse 为false代表从0-100的顺序 true为100到0的顺序寻找
  // -1 说明没有找到
  int _getNextSkipIndex(int index, {bool reverse = false}) {
    if (!reverse) {
      // 顺序从 0 到 100
      if (index > subtitles.length - 1) {
        return -1;
      }
      if (skipList.contains(index)) {
        return _getNextSkipIndex(index + 1, reverse: reverse);
      }
      return index;
    } else {
      // 逆序从 100 到 0
      if (index < 0) {
        return -1;
      }
      if (skipList.contains(index)) {
        return _getNextSkipIndex(index - 1, reverse: reverse);
      }
      return index;
    }
  }

  @override
  Future<void> play() async {
    logger("play() called");
  }

  @override
  Future<void> pause() async {
    logger("pause() called");
  }

  @override
  void playOrPause() {
    logger("playOrPause() called, current playing state: ${playing.value}");
  }

  @override
  void delaySubtitle(int milliseconds) {
    logger("delaySubtitle milliseconds = $milliseconds");
    subtitleController?.delaySubtitles(Duration(milliseconds: milliseconds));
    subtitles.value = subtitleController?.subtitles ?? [];
    subtitles.refresh();
  }

  @override
  void switchSubtitleMode() {
    currentSubtitleMode.value = (currentSubtitleMode.value + 1) % modes.length;
  }

  @override
  void showAudioService() {
    IPlayer.audioHandler?.ready();
  }

  @override
  void dismissAudioService() async {
    IPlayer.audioHandler?.idle();

    try {
      final session = await AudioSession.instance;
      // 释放音频焦点
      await session.setActive(false);
    } catch (e) {
      logger("dismissAudioService error $e");
    }
  }

  @override
  void toggleFullscreen() {
    logger("toggleFullscreen isLandscape=${Get.context?.isLandscape}");
    if (Get.context?.isLandscape == true) {
      exitNativeFullscreen();
    } else {
      enterNativeFullscreen();
    }
  }

  @override
  void disableOpenSingleRepeat() {
    openSingleRepeat.value = false;
  }

  @override
  void enableOpenSingleRepeat() {
    openSingleRepeat.value = true;
  }

  @override
  void switchOpenSingleRepeat() {
    if (openSingleRepeat.value) {
      disableOpenSingleRepeat();
    } else {
      enableOpenSingleRepeat();
    }
  }

  @override
  void disableOnlyPlayLines() {
    onlyPlayLines.value = false;
  }

  @override
  void enableOnlyPlayLines() {
    onlyPlayLines.value = true;
  }

  @override
  void switchOnlyPlayLines() {
    if (onlyPlayLines.value) {
      disableOnlyPlayLines();
    } else {
      enableOnlyPlayLines();
    }
  }

  @override
  Future<String?> screenshot2File() async {
    var image = await screenshot();
    if (image == null) {
      return null;
    }
    return FileUtils().saveTmpAudioServiceCover(image);
  }

  @override
  Future<void> enterNativeFullscreen() async {
    logger("enterNativeFullscreen");
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await Future.wait(
          [
            SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky, overlays: []),
            SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]),
          ],
        );
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        await const MethodChannel('com.alexmercerind/media_kit_video').invokeMethod(
          'Utils.EnterNativeFullscreen',
        );
      }
    } catch (exception, stacktrace) {
      logger(exception.toString());
      logger(stacktrace.toString());
    }
  }

  @override
  Future<void> exitNativeFullscreen() async {
    logger("exitNativeFullscreen");
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await Future.wait(
          [
            SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: SystemUiOverlay.values),
            SystemChrome.setPreferredOrientations(
              [DeviceOrientation.portraitUp],
            ),
          ],
        );
      } else if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
        await const MethodChannel('com.alexmercerind/media_kit_video').invokeMethod(
          'Utils.ExitNativeFullscreen',
        );
      }
      // unlockOrientation();
    } catch (exception, stacktrace) {
      logger(exception.toString());
      logger(stacktrace.toString());
    }
  }

  //TODO 效果不行 总是会先横屏再竖屏 先不使用
  @override
  Future<void> unlockOrientation() async {
    logger("unlockOrientation isUnlockingOrientation=$isUnlockingOrientation");
    if (isUnlockingOrientation) {
      return;
    }
    isUnlockingOrientation = true;
    await Future.delayed(const Duration(seconds: 3));
    if (Get.context?.isPortrait == true) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: SystemUiOverlay.values);
      SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    }
    isUnlockingOrientation = false;
  }

  @override
  void destory() {
    logger("Player onClose");
    SPUtil().saveHistory(_videoPath);
    reset();
  }
}

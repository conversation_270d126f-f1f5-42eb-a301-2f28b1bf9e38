import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/model/config/player.dart';
import 'package:lsenglish/model/setting/langs.dart';
import 'package:lsenglish/model/subtitle_cover_model.dart';
import 'package:lsenglish/model/user_login_resp/user.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/obs.dart';

import '../model/config/user_config.dart';
import '../net/net.dart';
import '../utils/sp.dart';

class Config {
  Config._internal();
  static final Config _instance = Config._internal();
  factory Config() => _instance;
  var themeMode = ThemeMode.light.obs;
  var isDev = true;
  //因为配置可能随时改变 所以不要初始化UserConfig来使用 而是直接使用
  UserConfig userConfig = UserConfig();
  PlayerConfig playerConfig = PlayerConfig();
  SubtitleCoverModel subtitleCoverModel = SubtitleCoverModel();
  late Stream<List<ConnectivityResult>> _connectivityStream;
  var netStateObs = <ConnectivityResult>[].obs;

  // 用户信息
  var currentUser = User().obs;

  var showSubtitleWhenRecordEnd = false.obs;
  var autoPlayRecordWhenRecordEnd = false.obs;
  var autoRecord = false.obs;
  var autoStopRecord = false.obs;
  var openSingleRepeat = false.obs;
  var coverSubtitle = false.obs;

  var _langs = LangsModel();
  LangsModel get langs => _langs;
  set langs(LangsModel value) {
    _langs = value;
  }

  void init() {
    final isDarkMode = GetStorage().read<bool>('isDarkMode') ?? false;
    Config().themeMode.value = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    _connectivityStream = Connectivity().onConnectivityChanged;
    _connectivityStream.listen((List<ConnectivityResult> result) {
      netStateObs.value = result;
    });
    updateData();
    if (isLogin()) {
      Net.getDio().options.headers['Authorization'] = SPUtil().getUserInfo()?.token;
      fetchConfig();
      fetchUserInfo();
    }
    ObsUtil().loginStatus.listen((v) {
      if (v) {
        fetchConfig();
        fetchUserInfo();
      }
    });
    _fetchLanguage();
  }

  void fetchConfig() {
    Net.getRestClient().config().then((value) {
      userConfig = value.data;
      playerConfig = value.data.playerConfig ?? PlayerConfig();
      updateData();
    });
  }

  // 获取用户信息
  void fetchUserInfo() {
    Net.getRestClient().userInfo().then((value) {
      currentUser.value = value.data;
      // 检查用户的语言设置
      checkUserLanguageSettings();
    });
  }

  // 检查用户语言设置
  void checkUserLanguageSettings() {
    final user = currentUser.value;
    if (user.nativeLangCode == null || user.nativeLangCode!.isEmpty || 
        user.targetLangCode == null || user.targetLangCode!.isEmpty) {
      // 跳转到语言设置页面
      Get.toNamed(Routes.LANGUAGE_SETTINGS);
    }
  }

  void _fetchLanguage() {
    Net.getRestClient().getLangs().then((value) {
      _langs = value.data;
    });
  }

  void updateData() {
    showSubtitleWhenRecordEnd = playerConfig.showSubtitleWhenRecordEnd.obs;
    autoPlayRecordWhenRecordEnd = playerConfig.autoPlayRecordWhenRecordEnd.obs;
    autoRecord = playerConfig.autoRecord.obs;
    autoStopRecord = playerConfig.autoStopRecord.obs;
    openSingleRepeat = playerConfig.openSingleRepeat.obs;
    coverSubtitle = playerConfig.coverSubtitle.obs;
  }

  void goFeedback() {}
  void setShowSubtitleWhenRecordEnd() {
    Config().playerConfig.showSubtitleWhenRecordEnd = !Config().playerConfig.showSubtitleWhenRecordEnd;
    showSubtitleWhenRecordEnd.value = Config().playerConfig.showSubtitleWhenRecordEnd;
    Net.getRestClient().updatePlayerConfig({'showSubtitleWhenRecordEnd': showSubtitleWhenRecordEnd.value});
  }

  void setAutoPlayRecordWhenRecordEnd() {
    Config().playerConfig.autoPlayRecordWhenRecordEnd = !Config().playerConfig.autoPlayRecordWhenRecordEnd;
    autoPlayRecordWhenRecordEnd.value = Config().playerConfig.autoPlayRecordWhenRecordEnd;
    Net.getRestClient().updatePlayerConfig({'autoPlayRecordWhenRecordEnd': autoPlayRecordWhenRecordEnd.value});
  }

  void setAutoRecord() {
    Config().playerConfig.autoRecord = !Config().playerConfig.autoRecord;
    autoRecord.value = Config().playerConfig.autoRecord;
    Net.getRestClient().updatePlayerConfig({'autoRecord': autoRecord.value});
  }

  void setAutoStopRecord() {
    Config().playerConfig.autoStopRecord = !Config().playerConfig.autoStopRecord;
    autoStopRecord.value = Config().playerConfig.autoStopRecord;
    Net.getRestClient().updatePlayerConfig({'autoStopRecord': autoStopRecord.value});
  }

  void setOpenSingleRepeat() {
    Config().playerConfig.openSingleRepeat = !Config().playerConfig.openSingleRepeat;
    openSingleRepeat.value = Config().playerConfig.openSingleRepeat;
    Net.getRestClient().updatePlayerConfig({'openSingleRepeat': openSingleRepeat.value});
  }

  void setCoverSubtitle() {
    Config().playerConfig.coverSubtitle = !Config().playerConfig.coverSubtitle;
    coverSubtitle.value = Config().playerConfig.coverSubtitle;
    Net.getRestClient().updatePlayerConfig({'coverSubtitle': coverSubtitle.value});
  }
}

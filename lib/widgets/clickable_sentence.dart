import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../utils/ls_word_util.dart';
import 'dict_widget.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

class ClickableSentence extends StatelessWidget {
  final String sentence;
  final TextStyle? style;
  final bool enableTranslate;
  final bool textSelectable;
  final bool isLandscape;
  final SpeechEvaluationResult? evalResult; // 新：完整评测结果

  const ClickableSentence({
    super.key,
    required this.sentence,
    this.style,
    this.textSelectable = true,
    this.enableTranslate = true,
    this.isLandscape = false,
    this.evalResult,
  });

  @override
  Widget build(BuildContext context) {
    final wordRegex = RegExp(r"\b\w+['']?\w*\b"); // 只匹配单词
    List<TextSpan> spans = [];
    int lastIndex = 0;

    // 优先用评测结果高亮
    List<SentEvalWord>? evalWordsSent;
    List<ParaEvalWordDetail>? evalWordsPara;
    List<WordEvalWord>? evalWordsWord;
    if (evalResult != null && evalResult!.result != null) {
      final result = evalResult!.result;
      logger("[ClickableSentence] 评测类型: ${result.runtimeType}");
      if (result is SentEvalResult && result.words != null) {
        evalWordsSent = result.words;
      } else if (result is ParaEvalResult && result.sentences != null && result.sentences!.isNotEmpty) {
        evalWordsPara = result.sentences!.expand((s) => s.details ?? <ParaEvalWordDetail>[]).toList();
      } else if (result is WordEvalResult && result.words != null) {
        evalWordsWord = result.words;
      }
    }
    if (evalWordsSent != null && evalWordsSent.isNotEmpty) {
      final compareResult = lsSentWordsCompareWithScore(sentence, evalWordsSent, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else if (evalWordsPara != null && evalWordsPara.isNotEmpty) {
      final compareResult = lsParaWordsCompareWithScore(sentence, evalWordsPara, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else if (evalWordsWord != null && evalWordsWord.isNotEmpty) {
      final compareResult = lsWordWordsCompareWithScore(sentence, evalWordsWord, isLandscape: isLandscape);
      spans = _buildSpans(context, compareResult.spans);
    } else {
      // 没有评测结果，普通分词但每个单词都可点击
      for (var match in wordRegex.allMatches(sentence)) {
        // 添加单词前的文本（包括空格和标点）
        if (match.start > lastIndex) {
          spans.add(TextSpan(
            text: sentence.substring(lastIndex, match.start),
            style: getStyle(),
          ));
        }
        // 添加可点击的单词
        final word = match.group(0)!;
        spans.add(TextSpan(
          text: word,
          style: getStyle(),
          recognizer: !enableTranslate
              ? null
              : (TapGestureRecognizer()
                ..onTap = () {
                  var wordTrim = _removePunctuation(word.trim());
                  logger("wordTrim = $wordTrim");
                  if (_isEnglishWord(wordTrim)) {
                    _showBottomSheet(context, wordTrim);
                  }
                }),
        ));
        lastIndex = match.end;
      }
      // 添加最后一个单词后的文本
      if (lastIndex < sentence.length) {
        spans.add(TextSpan(
          text: sentence.substring(lastIndex),
          style: getStyle(),
        ));
      }
    }
    return textSelectable
        ? SelectableText.rich(
            TextSpan(children: spans),
            textAlign: isLandscape ? TextAlign.center : null,
          )
        : Text.rich(
            TextSpan(children: spans),
            textAlign: isLandscape ? TextAlign.center : null,
          );
  }

  List<TextSpan> _buildSpans(BuildContext context, List<TextSpan> sourceSpans) {
    return sourceSpans.map((TextSpan span) {
      return TextSpan(
        text: span.text,
        style: span.style,
        recognizer: !enableTranslate
            ? null
            : (TapGestureRecognizer()
              ..onTap = () {
                var wordTrim = _removePunctuation(span.text.toString().trim());
                logger("wordTrim = $wordTrim");
                if (_isEnglishWord(wordTrim)) {
                  _showBottomSheet(context, wordTrim);
                }
              }),
      );
    }).toList();
  }

  bool _isEnglishWord(String word) {
    // 正则表达式用于检查是否为英文单词，允许撇号
    return RegExp(r"^[a-zA-Z']+$").hasMatch(word);
  }

  String _removePunctuation(String word) {
    // 正则表达式用于移除单词中的标点符号，除了撇号
    return word.replaceAll(RegExp(r"[^\w\s']"), '');
  }

  TextStyle? getStyle() {
    return style ?? Get.textTheme.titleLarge?.copyWith(fontSize: 19.whs, height: 1);
  }

  void _showBottomSheet(BuildContext context, String word, {bool autoPlayDict = true}) async {
    await Get.bottomSheet(
      DictWidget(word: word, autoPlayDict: autoPlayDict),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 2,
      barrierColor: Colors.black.withOpacity(0.5),
      isDismissible: true,
    );
  }
}

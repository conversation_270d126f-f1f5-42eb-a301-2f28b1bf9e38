import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';
import 'package:lsenglish/utils/size_extension.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/subtitle/subtitle.dart';

import '../utils/text_style.dart';
import 'clickable_sentence.dart';

///比如要学习英文，我的母语中文 targetLanguageStyle代表的是英文
class SplitEnglishWidget extends StatefulWidget {
  final Subtitle subtitle;
  final SpeechEvaluationResult? evalResult; // 评测结果
  //要学习的语言  英语
  final TextStyle? targetLanguageStyle;
  //母语  设置中文 那就是中文
  final TextStyle? nativeLanguageStyle;
  final bool useNativeShadows;
  final bool useTargetShadows;
  final int subtitleMode;
  final bool showPlaceholder;
  final bool enableTranslate;
  final GestureTapCallback? onPlaceholderTap;
  final bool textSelectable;
  final bool isLandscape;
  // 是否文字居中
  final bool centerText;

  const SplitEnglishWidget({
    super.key,
    required this.subtitle,
    this.evalResult,
    this.nativeLanguageStyle,
    this.targetLanguageStyle,
    this.useNativeShadows = false,
    this.useTargetShadows = false,
    this.subtitleMode = 0,
    this.showPlaceholder = false,
    this.enableTranslate = true,
    this.textSelectable = true,
    this.onPlaceholderTap,
    this.isLandscape = false,
    this.centerText = false,
  });

  @override
  State<SplitEnglishWidget> createState() => _SplitEnglishWidgetState();
}

class _SplitEnglishWidgetState extends State<SplitEnglishWidget> {
  late TextStyle nativeLanguageStyle;
  late TextStyle targetLanguageStyle;
  var showMotherLang = false;
  var showTargetLang = false;
  List<String> words = [];
  String englishText = "";
  String otherText = "";
  SpeechEvaluationResult? _currentResult;
  List<int> unPlaceholderList = [];
  @override
  void initState() {
    super.initState();
    notify();
  }

  void notify() {
    englishText = widget.subtitle.targetData;
    otherText = widget.subtitle.nativeData;
    _currentResult = widget.evalResult;
    words = englishText.split(' ');

    targetLanguageStyle =
        widget.targetLanguageStyle ?? getTargetSubtitleTextStyle(useShadows: widget.useTargetShadows, isLandscape: widget.isLandscape);
    nativeLanguageStyle =
        widget.nativeLanguageStyle ?? getNativeSubtitleTextStyle(useShadows: widget.useNativeShadows, isLandscape: widget.isLandscape);
    nativeLanguageStyle = targetLanguageStyle;
    showMotherLang =
        otherText.isNotEmpty && (widget.subtitleMode == SubtitleMode.bilingual.index || widget.subtitleMode == SubtitleMode.native.index);

    showTargetLang =
        englishText.isNotEmpty && (widget.subtitleMode == SubtitleMode.bilingual.index || widget.subtitleMode == SubtitleMode.target.index);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Widget content = Column(
      crossAxisAlignment: widget.centerText ? CrossAxisAlignment.center : CrossAxisAlignment.stretch,
      children: [
        widget.showPlaceholder
            ? Wrap(
                alignment: widget.centerText ? WrapAlignment.center : WrapAlignment.start,
                spacing: 4.whs,
                runSpacing: 4.whs,
                children: words
                    .map((word) => WordPlaceholder(
                          word: word,
                          textStyle: targetLanguageStyle,
                          onPlaceholderTap: widget.onPlaceholderTap,
                        ))
                    .toList(),
              )
            : Visibility(
                visible: showTargetLang,
                child: ClickableSentence(
                  isLandscape: widget.isLandscape,
                  enableTranslate: widget.enableTranslate,
                  sentence: englishText,
                  evalResult: _currentResult,
                  style: targetLanguageStyle,
                  textSelectable: widget.textSelectable,
                ),
              ),
        showMotherLang && !widget.showPlaceholder
            ? Padding(
                padding: EdgeInsets.only(top: showMotherLang ? (widget.isLandscape ? 6.whs : 8.whs) : 0),
                child: Text(
                  otherText,
                  style: nativeLanguageStyle,
                  textAlign: widget.centerText ? TextAlign.center : TextAlign.start,
                ),
              )
            : const SizedBox.shrink()
      ],
    );

    return content;
  }

  @override
  void didUpdateWidget(covariant SplitEnglishWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.subtitleMode != widget.subtitleMode ||
        oldWidget.subtitle != widget.subtitle ||
        oldWidget.targetLanguageStyle != widget.targetLanguageStyle ||
        oldWidget.nativeLanguageStyle != widget.nativeLanguageStyle ||
        oldWidget.useNativeShadows != widget.useNativeShadows ||
        oldWidget.isLandscape != widget.isLandscape ||
        oldWidget.centerText != widget.centerText ||
        oldWidget.evalResult != widget.evalResult ||
        oldWidget.useTargetShadows != widget.useTargetShadows) {
      notify();
    }
  }
}

class WordPlaceholder extends StatelessWidget {
  final String word;
  final TextStyle textStyle;
  final GestureTapCallback? onPlaceholderTap;

  const WordPlaceholder({super.key, required this.word, required this.textStyle, this.onPlaceholderTap});

  @override
  Widget build(BuildContext context) {
    final textSpan = TextSpan(text: word, style: textStyle);
    final textPainter = TextPainter(text: textSpan, textDirection: TextDirection.ltr);
    textPainter.layout(minWidth: 0, maxWidth: double.infinity);
    final wordWidth = textPainter.width;
    return GestureDetector(
      onTap: onPlaceholderTap,
      child: Container(
        constraints: BoxConstraints(minWidth: 20.0.whs),
        width: wordWidth,
        height: textStyle.fontSize! * 1.2,
        decoration: BoxDecoration(
          color: Get.isDarkMode ? Colors.white.withValues(alpha: 0.15) : const Color(0xffF2F4F7),
          borderRadius: BorderRadius.all(Radius.circular(4.whs)),
        ),
      ),
    );
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/model/english_dict_model/english_rel_word_model/rel_sub.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/size_extension.dart';

import '../app/modules/detail/controllers/detail_controller.dart';
import '../model/english_dict_model/english_dict_model.dart';
import '../model/english_dict_model/english_ec_model/trs.dart';
import '../model/english_dict_model/english_ec_model/wfs.dart';
// https://www.shanxing.top/archives/40
// 单词音频接口：
// http://dict.youdao.com/dictvoice?type=音频类型&audio=单词
// 请求方法：get
// type=0 : 美音
// type=1 : 英音
// audio : 单词

// collins — 柯林斯英汉双解大辞典
// ec — 英汉翻译字典（英汉释义）
// ee — 英英翻译字典（英英释义）
// exam_dict — 考试专用字典（词汇词根+联想记忆法）
// longman — 朗文当代高级英语辞典
// phrs — 词组短语，如 account 单词对应的“current account”
// rel_word — 同根词，如 account 单词对应的形容词“accountable”
// syno — 近义词，单词每种释义对应的近义词列表
// web_trans — 网络释义

// "blng_sents_part",//双语例句
// auth_sents_part 权威例句
// rel_word 同根词
// syno 同近义词
// ee 英英释义

// ```json
// {
//   "count": 99,
//   "dicts": [
//     ["ec", "collins", "input", "ee", "syno", "rel_word", "blng_sents_part"],
//     ["web_trans"]
//   ]
// }
class DictWidget extends StatefulWidget {
  final String word;
  final bool autoPlayDict;
  const DictWidget({super.key, required this.word, required this.autoPlayDict});

  @override
  State<DictWidget> createState() => _DictWidgetState();
}

class _DictWidgetState extends State<DictWidget> {
  var usphone = "".obs;
  var ukphone = "".obs;
  var simple = "".obs;
  var wfs = <Wfs>[].obs;
  var trsList = <Trs>[].obs;
  var relList = <RelSub>[].obs;
  bool isClosing = false;
  bool canHandleDragDismiss = true;
  bool scrollEnd = true;
  FlutterSoundPlayer soundPlayer = FlutterSoundPlayer(logLevel: Level.off);
  @override
  void initState() {
    super.initState();
    if (Get.isRegistered<DetailController>()) {
      Get.find<DetailController>().videoKit.pause();
    }

    fetch();
  }

  @override
  void dispose() {
    soundPlayer.closePlayer();
    super.dispose();
  }

  void fetch() async {
    final results = await Future.wait([
      Net.getRestClient().getEnglishDict("2", "mobile", widget.word, "wifi", "5.1",
          "%7B%22count%22%3A99%2C%22dicts%22%3A%5B%5B%22ec%22%2C%22collins%22%2C%22input%22%2C%22ee%22%2C%22syno%22%2C%22rel_word%22%2C%22blng_sents_part%22%5D%2C%5B%22web_trans%22%5D%5D%7D"),
      soundPlayer.openPlayer(),
    ]);

    final dictData = results[0] as EnglishDictModel;
    _initData(dictData);

    await soundPlayer.openPlayer();
  }

  void _initData(EnglishDictModel englishDictModel) {
    logger("englishDictModel.ec?.source?.name = ${englishDictModel.ec?.source?.name}");
    usphone.value = englishDictModel.simple?.word?.firstOrNull?.usphone ?? "";
    ukphone.value = englishDictModel.simple?.word?.firstOrNull?.ukphone ?? "";
    // simple.value = englishDictModel.ec?.word?.firstOrNull?.trs?.firstOrNull?.tr?.firstOrNull?['l']['i'].firstOrNull ?? "";
    logger("englishDictModel.ec?.word?.firstOrNull?.wfs = ${jsonEncode(englishDictModel.ec?.word?.firstOrNull?.wfs)}");
    wfs.value = englishDictModel.ec?.word?.firstOrNull?.wfs ?? [];
    trsList.value = englishDictModel.ec?.word?.firstOrNull?.trs ?? [];
    relList.value = englishDictModel.relWord?.rels
            ?.where((rel) => rel.rel != null) // Filter out null RelSub
            .map((rel) => rel.rel!)
            .toList() ??
        [];
    if (widget.autoPlayDict) {
      playWord(0);
    }
  }

  void playWord(int type) {
    String url = "https://dict.youdao.com/dictvoice?type=$type&audio=${widget.word}";
    logger("playWord $url");
    try {
      soundPlayer.startPlayer(fromURI: url);
    } catch (e) {
      logger("playWord 单词播放失败: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => Get.back(),
          child: Container(color: Colors.transparent),
        ),
        Padding(
          padding: EdgeInsets.only(top: Get.height / 10),
          child: DraggableScrollableSheet(
              initialChildSize: 0.5,
              minChildSize: 0.5,
              maxChildSize: 1.0,
              builder: (context, scrollController) {
                return NotificationListener<DraggableScrollableNotification>(
                  onNotification: (notification) {
                    canHandleDragDismiss = notification.extent <= notification.minExtent;
                    scrollEnd = false;
                    if (notification.extent >= 1.0 && notification.extent > notification.minExtent) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        scrollController.animateTo(0.5, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
                      });
                    }
                    return true;
                  },
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (notification) {
                      if (notification is ScrollEndNotification) {
                        scrollEnd = true;
                      }
                      //scrollEnd 是判断是否结束了滚动操作 如果是在滚动过程中不触发
                      //isClosing 确保只被Back一次
                      //canHandleDragDismiss 判断什么时候能够被结束 也就是minChildSize的时候
                      if (notification is ScrollUpdateNotification) {
                        if (scrollEnd &&
                            canHandleDragDismiss &&
                            !isClosing &&
                            notification.metrics.extentBefore == 0 &&
                            notification.scrollDelta! > 0) {
                          isClosing = true;
                          Get.back();
                        }
                      }
                      return true;
                    },
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.9,
                        color: Get.theme.scaffoldBackgroundColor,
                        child: SingleChildScrollView(
                          controller: scrollController,
                          child: Column(
                            children: [
                              Container(
                                margin: EdgeInsets.symmetric(vertical: 15.whs),
                                width: 32.whs,
                                height: 6.whs,
                                decoration: BoxDecoration(
                                  color: Get.theme.primaryColor,
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                              Center(
                                child: Obx(() => Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Text(widget.word, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                                          GestureDetector(
                                            onTap: () => playWord(0),
                                            child: Container(
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(
                                                  color: Get.theme.primaryColor.withOpacity(0.5),
                                                  borderRadius: BorderRadius.circular(4),
                                                ),
                                                padding: EdgeInsets.all(8.whs),
                                                child: Text("美:${usphone.value}", style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold))),
                                          ),
                                          Gap(8.whs),
                                          GestureDetector(
                                            onTap: () => playWord(1),
                                            child: Container(
                                                clipBehavior: Clip.antiAlias,
                                                decoration: BoxDecoration(
                                                  color: Get.theme.primaryColor.withOpacity(0.5),
                                                  borderRadius: BorderRadius.circular(4),
                                                ),
                                                padding: EdgeInsets.all(8.whs),
                                                child: Text("英:${ukphone.value}", style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold))),
                                          ),
                                          Gap(8.whs),
                                          Text("简明", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.bold)),
                                          Gap(8.whs),
                                          // Text("美:${simple.value}", style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold)),
                                          ListView.builder(
                                            physics: const NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: trsList.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              return Text(trsList[index].tr?.firstOrNull?.l?.i?.firstOrNull ?? "",
                                                  style: const TextStyle(fontSize: 15));
                                            },
                                          ),
                                          Gap(8.whs),
                                          ListView.builder(
                                            physics: const NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: wfs.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              return Text((wfs[index].wf?.name ?? "") + (wfs[index].wf?.value ?? ""),
                                                  style: const TextStyle(fontSize: 15));
                                            },
                                          ),
                                          Text("同根词", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.bold)),
                                          // relList
                                          ListView.builder(
                                            physics: const NeverScrollableScrollPhysics(),
                                            shrinkWrap: true,
                                            itemCount: relList.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              String getWordsText(RelSub relSub) {
                                                if (relSub.words == null) return "";

                                                return relSub.words!.map((wordMap) {
                                                  String word = wordMap['word'] ?? '';
                                                  String tran = wordMap['tran'] ?? '';
                                                  return '$word $tran';
                                                }).join(' ');
                                              }

                                              return Text((relList[index].pos ?? "") + getWordsText(relList[index]),
                                                  style: const TextStyle(fontSize: 15));
                                            },
                                          ),
                                          // Text("柯林斯", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.bold)),
                                        ],
                                      ),
                                    )),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }),
        ),
      ],
    );
  }
}

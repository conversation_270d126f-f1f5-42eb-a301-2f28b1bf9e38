import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

////////////////////////////////////////////////////////////////////////////////
///垂直滑动的 ViewPage 里嵌套垂直滑动的 ListView

typedef NestPageViewWidgetBuilder = Widget Function(BuildContext context, PageController pageController, ScrollController scrollController);
typedef ScrollDirectionCallback = void Function(bool isForward);

class NestPageView extends StatefulWidget {
  final NestPageViewWidgetBuilder builder;
  final PageController? pageController;
  final VoidCallback? onUserScrollStart;
  final VoidCallback? onUserScrollEnd;
  final ScrollDirectionCallback? onPageScrollDirection;

  const NestPageView({
    super.key,
    required this.builder,
    this.pageController,
    this.onUserScrollStart,
    this.onUserScrollEnd,
    this.onPageScrollDirection,
  });

  @override
  State<NestPageView> createState() => _NestPageViewState();
}

class _NestPageViewState extends State<NestPageView> {
  late PageController _pageController;
  late ScrollController _listScrollController;
  var useCustomPageController = false;
  ScrollController? _activeScrollController;
  Drag? _drag;
  bool _isScrolling = false;
  double _previousPagePosition = 0.0;
  Map<int, ScrollController> indexScrollController = {};

  Timer? _debounceTimer;
  //内容是否超过了一页
  var isContentExceedingPage = false;
  @override
  void initState() {
    super.initState();
    useCustomPageController = widget.pageController != null;
    _pageController = widget.pageController ?? PageController();
    _listScrollController = ScrollController();

    _pageController.addListener(_scrollListener);
    _listScrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _pageController.removeListener(_scrollListener);
    _listScrollController.removeListener(_scrollListener);
    if (!useCustomPageController) {
      _pageController.dispose();
    }
    _listScrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  bool contentExceedingPage(ScrollController listScrollController, PageController pageController) {
    // 获取 listScrollController 的最大滚动范围
    double listViewMaxScrollExtent = listScrollController.position.maxScrollExtent;

    // 获取 pageController 的视口高度
    double pageViewHeight = pageController.position.viewportDimension;

    // 判断内容是否超过
    return listViewMaxScrollExtent > pageViewHeight;
  }

  void _scrollListener() {
    if (_activeScrollController?.position.isScrollingNotifier.value == false) {
      if (_isScrolling) {
        _isScrolling = false;
      }
    } else {
      _isScrolling = true;
    }
    if (_activeScrollController == _pageController) {
      double currentPagePosition = _pageController.page ?? 0.0;
      if (currentPagePosition != _previousPagePosition) {
        bool isForward = currentPagePosition > _previousPagePosition;
        widget.onPageScrollDirection?.call(isForward);
        _previousPagePosition = currentPagePosition;
      }
    }
  }

  void _handleDragStart(DragStartDetails details) {
    if (!isContentExceedingPage) {
      _activeScrollController = _pageController;
      _drag = _pageController.position.drag(details, _disposeDrag);
      return;
    }

    // ///先判断 Listview 是否可见或者可以调用
    // ///一般不可见时 hasClients false ，因为 PageView 也没有 keepAlive
    if (_listScrollController.hasClients == true) {
      ///获取 ListView 的  renderBox
      final RenderBox? renderBox = _listScrollController.position.context.storageContext.findRenderObject() as RenderBox?;

      ///判断触摸的位置是否在 ListView 内
      ///不在范围内一般是因为 ListView 已经滑动上去了，坐标位置和触摸位置不一致
      if (renderBox?.paintBounds.shift(renderBox.localToGlobal(Offset.zero)).contains(details.globalPosition) == true) {
        _activeScrollController = _listScrollController;
        _drag = _activeScrollController?.position.drag(details, _disposeDrag);
        return;
      }
    }

    ///这时候就可以认为是 PageView 需要滑动
    _activeScrollController = _pageController;
    _drag = _pageController.position.drag(details, _disposeDrag);
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    if (_activeScrollController == _listScrollController) {
      // Check if scrolling up and at the bottom
      if (details.primaryDelta! < 0 &&
          //到了底部，切换到 PageView
          _activeScrollController?.position.pixels == _activeScrollController?.position.maxScrollExtent) {
        //切换相应的控制器
        _activeScrollController = _pageController;
        _drag?.cancel();
        _drag = _pageController.position
            .drag(DragStartDetails(globalPosition: details.globalPosition, localPosition: details.localPosition), _disposeDrag);
      }
      // Check if scrolling down and at the top
      else if (details.primaryDelta! > 0 &&
          //到了顶部，切换到 PageView
          _activeScrollController?.position.pixels == _activeScrollController?.position.minScrollExtent) {
        ///切换相应的控制器
        _activeScrollController = _pageController;
        _drag?.cancel();

        ///参考  Scrollable 里的，
        ///因为是切换控制器，也就是要更新 Drag
        ///拖拽流程要切换到 PageView 里，所以需要  DragStartDetails
        ///所以需要把 DragUpdateDetails 变成 DragStartDetails
        ///提取出 PageView 里的 Drag 相应 details
        _drag = _pageController.position
            .drag(DragStartDetails(globalPosition: details.globalPosition, localPosition: details.localPosition), _disposeDrag);
      }
    }
    _drag?.update(details);
  }

  void _handleDragEnd(DragEndDetails details) async {
    _drag?.end(details);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      isContentExceedingPage = contentExceedingPage(_listScrollController, _pageController);
    });
  }

  void _handleDragCancel() {
    _drag?.cancel();
  }

  ///拖拽结束了，释放  _drag
  void _disposeDrag() {
    _drag = null;
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollStartNotification) {
          _isScrolling = true;
          widget.onUserScrollStart?.call();
        } else if (notification is ScrollEndNotification) {
          if (_isScrolling) {
            _isScrolling = false;
            widget.onUserScrollEnd?.call();
          }
        }
        return true;
      },
      child: RawGestureDetector(
        gestures: <Type, GestureRecognizerFactory>{
          VerticalDragGestureRecognizer: GestureRecognizerFactoryWithHandlers<VerticalDragGestureRecognizer>(() => VerticalDragGestureRecognizer(),
              (VerticalDragGestureRecognizer instance) {
            instance
              ..onStart = _handleDragStart
              ..onUpdate = _handleDragUpdate
              ..onEnd = _handleDragEnd
              ..onCancel = _handleDragCancel;
          })
        },
        behavior: HitTestBehavior.opaque,
        child: widget.builder(context, _pageController, _listScrollController),
      ),
    );
  }
}

import 'package:flutter/material.dart';

ThemeData get lightThemeData => ThemeData(
      primaryColor: const Color(0xff14AE5C),
      appBarTheme: const AppBarTheme(
        iconTheme: IconThemeData(
          color: Color(0xff21005D),
          size: 24,
        ),
        color: Color(0xFFF6F6F6),
        surfaceTintColor: Color(0xFFF6F6F6),
      ),
      scaffoldBackgroundColor: Colors.white,
      navigationBarTheme: const NavigationBarThemeData(backgroundColor: Colors.white),
      colorScheme: lightColorScheme,
      useMaterial3: true,
      brightness: Brightness.light,
      splashFactory: NoSplash.splashFactory,
      dividerColor: gray200,
      dividerTheme: DividerThemeData(
        color: gray200,
      ),
      iconTheme: const IconThemeData(color: Colors.black),
      textTheme: const TextTheme(
        titleMedium: TextStyle(color: Colors.black),
      ),
    );

ThemeData get darkThemeData => ThemeData(
      primaryColor: const Color(0xff14AE5C),
      appBarTheme: const AppBarTheme(
          iconTheme: IconThemeData(
            color: Colors.white,
            size: 24,
          ),
          color: Colors.black,
          surfaceTintColor: Colors.black),
      scaffoldBackgroundColor: Colors.black,
      navigationBarTheme: const NavigationBarThemeData(backgroundColor: Colors.black),
      colorScheme: dartColorScheme,
      useMaterial3: true,
      brightness: Brightness.dark,
      splashFactory: NoSplash.splashFactory,
      dividerColor: gray200,
      dividerTheme: DividerThemeData(
        color: gray200,
      ),
      iconTheme: const IconThemeData(color: Colors.white),
      textTheme: const TextTheme(
        titleMedium: TextStyle(color: Colors.white),
      ),
    );

ColorScheme get lightColorScheme => const ColorScheme.light(
      background: Colors.white,
      primary: Color(0xff14AE5C),
      secondary: Color(0xff625B71),
      onSecondary: Color(0xff1D192B),
      surface: Colors.white,
      error: Color(0xffFF4015),
    );

ColorScheme get dartColorScheme => const ColorScheme.dark(
      background: Colors.black87,
      primary: Color(0xff14AE5C),
      surface: Colors.black87,
      error: Color(0xffFF4015),
    );

Color get gray25 => const Color(0xffFCFCFD);
Color get gray50 => const Color(0xffF9FAFB);
Color get gray100 => const Color(0xffF2F4F7);
Color get gray200 => const Color(0xffE4E7EC);
Color get gray300 => const Color(0xffD0D5DD);
Color get gray400 => const Color(0xff98A2B3);
Color get gray500 => const Color(0xff667085);
Color get gray600 => const Color(0xff475467);
Color get gray700 => const Color(0xff344054);
Color get gray800 => const Color(0xff1D2939);
Color get gray900 => const Color(0xff101828);

Color get brand25 => const Color(0xffFCFAFF);
Color get brand50 => const Color(0xffF9F5FF);
Color get brand100 => const Color(0xffF4EBFF);
Color get brand200 => const Color(0xffE9D7FE);
Color get brand300 => const Color(0xffD6BBFB);
Color get brand400 => const Color(0xffB692F6);
Color get brand500 => const Color(0xff9E77ED);
Color get brand600 => const Color(0xff7F56D9);
Color get brand700 => const Color(0xff6941C6);
Color get brand800 => const Color(0xff53389E);
Color get brand900 => const Color(0xff42307D);

Color get red25 => const Color(0xffFFFBFA);
Color get red50 => const Color(0xffFEF3F2);
Color get red100 => const Color(0xffFEE4E2);
Color get red200 => const Color(0xffFECDCA);
Color get red300 => const Color(0xffFDA29B);
Color get red400 => const Color(0xffF97066);
Color get red500 => const Color(0xffF04438);
Color get red600 => const Color(0xffD92D20);
Color get red700 => const Color(0xffB42318);
Color get red800 => const Color(0xff912018);
Color get red900 => const Color(0xff7A271A);

Color get green25 => const Color(0xffF6FEF9);
Color get green50 => const Color(0xffECFDF3);
Color get green100 => const Color(0xffD1FADF);
Color get green200 => const Color(0xffA6F4C5);
Color get green300 => const Color(0xff6CE9A6);
Color get green400 => const Color(0xff32D583);
Color get green500 => const Color(0xff12B76A);
Color get green600 => const Color(0xff039855);
Color get green700 => const Color(0xff027A48);
Color get green800 => const Color(0xff05603A);
Color get green900 => const Color(0xff054F31);

Color get warn25 => const Color(0xffFFFCF5);
Color get warn50 => const Color(0xffFFFAEB);
Color get warn100 => const Color(0xffFEF0C7);
Color get warn200 => const Color(0xffFEDF89);
Color get warn300 => const Color(0xffFEC84B);
Color get warn400 => const Color(0xffFDB022);
Color get warn500 => const Color(0xffF79009);
Color get warn600 => const Color(0xffDC6803);
Color get warn700 => const Color(0xffB54708);
Color get warn800 => const Color(0xff93370D);
Color get warn900 => const Color(0xff7A2E0E);

Color get blue25 => const Color(0xffF5FAFF);
Color get blue50 => const Color(0xffEFF8FF);
Color get blue100 => const Color(0xffD1E9FF);
Color get blue200 => const Color(0xffB2DDFF);
Color get blue300 => const Color(0xff84CAFF);
Color get blue400 => const Color(0xff53B1FD);
Color get blue500 => const Color(0xff2E90FA);
Color get blue600 => const Color(0xff1570EF);
Color get blue700 => const Color(0xff175CD3);
Color get blue800 => const Color(0xff1849A9);
Color get blue900 => const Color(0xff194185);

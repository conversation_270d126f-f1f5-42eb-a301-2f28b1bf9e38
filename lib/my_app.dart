import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/app/modules/splash/bindings/splash_binding.dart';
import 'app/modules/detail/bindings/detail_binding.dart';
import 'app/routes/app_pages.dart';
import 'generated/locales.g.dart';
import 'theme.dart';
import 'utils/lang.dart';
import 'utils/screen.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    if (context.isTablet) {
      MyScreenUtil.init(835, 1194);
    } else {
      MyScreenUtil.init(375, 812);
    }

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: "LS100",
      themeMode: GetStorage().read<bool>("isDarkMode") == true ? ThemeMode.dark : ThemeMode.light,
      builder: FlutterSmartDialog.init(),
      theme: lightThemeData,
      darkTheme: darkThemeData,
      initialBinding: SplashBinding(),
      initialRoute: Routes.SPLASH,
      getPages: AppPages.routes,
      translationsKeys: AppTranslation.translations,
      locale: getLang(),
      fallbackLocale: getLang(),
      navigatorKey: navigatorState,
    );
  }
}

import 'dart:async';

import 'package:get/get.dart';

class BaseStreamController extends GetxController {
  final List<StreamSubscription?> subscriptions = [];
  @override
  void onInit() {
    super.onInit();
    subscriptions.clear();
    subscriptions.addAll([]);
  }

  @override
  void onClose() {
    for (final subscription in subscriptions) {
      subscription?.cancel();
    }
    super.onClose();
  }
}

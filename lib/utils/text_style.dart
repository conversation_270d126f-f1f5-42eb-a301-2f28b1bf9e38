import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/utils/size_extension.dart';

TextStyle getTargetSubtitleTextStyle({bool isLandscape = false, bool useShadows = false}) {
  List<Shadow> getShadows() {
    return [
      Shadow(
        color: Colors.black.withValues(alpha: 0.76),
        offset: const Offset(0, 0.7),
        blurRadius: 1.0,
      ),
    ];
  }

  return TextStyle(
    color: isLandscape
        ? Colors.white
        : Get.isDarkMode
            ? Colors.white
            : Colors.black,
    letterSpacing: -0.5,
    fontSize: isLandscape ? 16.whs : 18,
    height: isLandscape ? 1 : 1.2,
    fontWeight: FontWeight.w600,
    shadows: useShadows ? getShadows() : null,
  );
}

TextStyle getNativeSubtitleTextStyle({bool isLandscape = false, bool useShadows = false}) {
  List<Shadow> getShadows() {
    return [
      Shadow(
        color: Colors.black.withValues(alpha: 0.76),
        offset: const Offset(0, 0.7),
        blurRadius: 1.0,
      ),
    ];
  }

  return TextStyle(
    color: isLandscape
        ? Colors.white
        : Get.isDarkMode
            ? Colors.white.withAlpha(125)
            : Get.theme.colorScheme.secondary,
    letterSpacing: -0.5,
    fontSize: isLandscape ? 14.whs : 16.whs,
    height: isLandscape ? 1 : 1.2,
    fontWeight: FontWeight.w400,
    shadows: useShadows ? getShadows() : null,
  );
}

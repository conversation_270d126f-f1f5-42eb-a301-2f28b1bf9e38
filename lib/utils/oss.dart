import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:convert';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../config/config.dart';

class OssUtil {
  // 私有构造函数，防止类被实例化
  OssUtil._internal();

  // 唯一实例，通过工厂构造函数获取
  static final OssUtil instance = OssUtil._internal();

  // 工厂构造函数
  factory OssUtil() => instance;

  static String ossAccessKeyId = "LTAI5tKfigrpM3jXMbni99Yh";

  static String ossAccessKeySecret = '******************************';

  static String userResourcePath = 'user-resource';
  static String prodBucket = "seedtu-prod";
  static String devBucket = "seedtu-dev";
  static String prodBucketUrl = "https://$prodBucket.oss-cn-hangzhou.aliyuncs.com";
  static String devBucketUrl = "https://$devBucket.oss-cn-hangzhou.aliyuncs.com";
  String _getUid() {
    return Config().currentUser.value.id?.isNotEmpty == true ? "/${Config().currentUser.value.id}" : "";
  }

  Future<String?> uploadUserPrivateVideoFile(String filePath) async {
    final String? url = await _upload(file: File(filePath), rootDir: "$userResourcePath${_getUid()}/user-private-video");
    debugPrint(url);
    return url;
  }

  Future<String?> uploadUserRecordFile(String filePath) async {
    final String? url = await _upload(file: File(filePath), rootDir: "$userResourcePath${_getUid()}/speech-evaluation");
    debugPrint(url);
    return url;
  }

  Future<String?> uploadSubtitleFile(String filePath) async {
    final String? url = await _upload(file: File(filePath), rootDir: "$userResourcePath${_getUid()}/subtitle");
    debugPrint(url);
    return url;
  }

  Future<String?> uploadUserAvatarFile(String filePath) async {
    final String? url = await _upload(file: File(filePath), rootDir: "$userResourcePath${_getUid()}/avatar");
    debugPrint(url);
    return url;
  }

  Future<String?> uploadUserLogFile(String filePath) async {
    final String? url = await _upload(file: File(filePath), rootDir: "$userResourcePath${_getUid()}/logs");
    debugPrint(url);
    return url;
  }

  /// @params file 要上传的文件对象
  /// @params rootDir 阿里云oss设置的根目录文件夹名字
  /// @param fileType 文件类型例如jpg,mp4等
  /// @param callback 回调函数我这里用于传cancelToken，方便后期关闭请求
  /// @param onSendProgress 上传的进度事件
  /// 参考文档https://help.aliyun.com/zh/oss/developer-reference/postobject#section-mcg-hq4-y1k
  static Future<String?> _upload({
    required File file,
    String rootDir = '',
  }) async {
    DateTime currentTime = DateTime.now().toUtc();
    DateTime expirationTime = currentTime.add(const Duration(days: 1));
    String expiration = expirationTime.toIso8601String();
    var bucket = Config().isDev ? devBucket : prodBucket;
    String policyText = '{"expiration": "$expiration","conditions": [{"bucket": "$bucket" },["content-length-range", 0, 1048576000]]}';
    // 获取签名
    String signature = getSignature(policyText);
    BaseOptions options = BaseOptions();
    options.responseType = ResponseType.plain;
    //创建dio对象
    Dio dio = Dio(options);
    dio.interceptors.add(PrettyDioLogger(
      requestHeader: true,
      requestBody: true,
      request: true,
    ));
    String pathName = '$rootDir/${getDate()}/${getRandom(12)}.${getFileType(file.path)}';
    // 请求参数的form对象
    FormData data = FormData.fromMap({
      'key': pathName,
      'policy': getPolicyBase64(policyText),
      'OSSAccessKeyId': ossAccessKeyId,
      'success_action_status': '200', //让服务端返回200，不然，默认会返回204
      'signature': signature,
      'contentType': 'multipart/form-data',
      'file': MultipartFile.fromFileSync(file.path),
    });

    Response response;
    CancelToken uploadCancelToken = CancelToken();
    try {
      // 发送请求
      var url = Config().isDev ? devBucketUrl : prodBucketUrl;
      response = await dio.post(url, data: data, cancelToken: uploadCancelToken, onSendProgress: (int count, int data) {});
      if (response.statusCode == 200) {
        debugPrint('文件上传成功${response.data}');
      }
      // 成功后返回文件访问路径
      return '$url/$pathName';
    } catch (e) {
      debugPrint("上传文件失败: ${e.toString()}");
    }
    return null;
  }

  /// 下载文件（图片、视频等
  /// url:文件url
  Future<String?> download({required String url, required String fileType}) async {
    BaseOptions options = BaseOptions();
    options.responseType = ResponseType.bytes;
    //创建dio对象
    Dio dio = Dio(options);
    Response<Uint8List> response;

    try {
      // 发送请求
      response = await dio.get<Uint8List>(url); //图片或视频的oss地址
      // 存储到本地
      _saveImageToFile(response, fileType);
      debugPrint('Image downloaded successfully to: ${response.data}');
      // 成功后返回文件访问路径
      return url;
    } catch (e) {
      debugPrint(e.toString());
    }
    return null;
  }

  Future<void> _saveImageToFile(Response response, String fileType) async {
    try {
      Directory appDir = await getApplicationDocumentsDirectory();
      String filePath = '${appDir.path}/image.$fileType';
      File file = File(filePath);
      await file.writeAsBytes(response.data);
      debugPrint('Image saved to file: $filePath');
    } catch (error) {
      debugPrint('Error while saving image: $error');
    }
  }

  /*
  * 生成固定长度的随机字符串
  * */
  static String getRandom(int num) {
    String alphabet = 'abcdefghijklmnopqlstuvwxyzABCDEFGHIJKLMNOPQLSTUVWXYZ';
    String left = '';
    for (var i = 0; i < num; i++) {
//    right = right + (min + (Random().nextInt(max - min))).toString();
      left = left + alphabet[Random().nextInt(alphabet.length)];
    }
    return left;
  }

  /*
  * 根据图片本地路径获取图片名称
  * */
  static String? getImageNameByPath(String? filePath) {
    return filePath?.substring(filePath.lastIndexOf("/") + 1, filePath.length);
  }

  /// 获取文件类型
  static String getFileType(String path) {
    debugPrint(path);
    List<String> array = path.split('.');
    return array[array.length - 1];
  }

  /// 获取日期
  static String getDate() {
    DateTime now = DateTime.now();
    return '${now.year}${now.month}${now.day}';
  }

  // 获取policy的base64
  static getPolicyBase64(String policyText) {
    //进行utf8编码
    List<int> policytextUtf8 = utf8.encode(policyText);
    //进行base64编码
    String policyBase64 = base64.encode(policytextUtf8);
    return policyBase64;
  }

  /// 获取签名
  static String getSignature(String policyText) {
    //进行utf8编码
    List<int> policytextUtf8 = utf8.encode(policyText);
    //进行base64编码
    String policyBase64 = base64.encode(policytextUtf8);
    //再次进行utf8编码
    List<int> policy = utf8.encode(policyBase64);
    //进行utf8 编码
    List<int> key = utf8.encode(ossAccessKeySecret);
    //通过hmac,使用sha1进行加密
    List<int> signaturePre = Hmac(sha1, key).convert(policy).bytes;
    //最后一步，将上述所得进行base64 编码
    String signature = base64.encode(signaturePre);
    return signature;
  }
}

{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b9a5f7603787ef142c452af9235a21c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983804e6cd7f75fb43a6e9d1a6c550be59", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e3cb96b7cce1876051a43afc37f21285", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e691ab98c04bf8f78785357023d4712", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e3cb96b7cce1876051a43afc37f21285", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987bb50025ccebba609e2fb6798dbe6008", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d3ad2fc8e0815867cfed8ccfdebb8ab", "guid": "bfdfe7dc352907fc980b868725387e98be3183c7fd5efc0e17334c30b5a032d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca12e388bf38046485f1ca6c01542a3b", "guid": "bfdfe7dc352907fc980b868725387e98ef30b0cf5090a5d752512874e023d3f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8d336935028675a420abad5e5b2a52", "guid": "bfdfe7dc352907fc980b868725387e98ee12095d99e719322c03cb71635fb6b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ad61b3fd98ba3d0997bbe67d356943", "guid": "bfdfe7dc352907fc980b868725387e98d2c31ad39e3702355453b2a24c398a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e66eb58b4550595352c1f8fc6dae35", "guid": "bfdfe7dc352907fc980b868725387e9897a5da444a45d45f6535f43e617b2ae5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6521a12476c8f0031b97f17cc7484bd", "guid": "bfdfe7dc352907fc980b868725387e9889831a1450b9bfcb18dcdca564f0d82f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a8bd21dc006846fad921dcc16e49dc1d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985d8ae23d3746bfc307766105a8697094", "guid": "bfdfe7dc352907fc980b868725387e98af40ea7e075d4a138471342c17c9b4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da189bc66636d9da6775ef469c9eb500", "guid": "bfdfe7dc352907fc980b868725387e980ab526925bc52aa439bd77c0ffc76877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39a68ec06fc769ce8558b673f69af23", "guid": "bfdfe7dc352907fc980b868725387e98fc32fe99b61d9d68a29316073cbbc753"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b585347e578c9c600a14a28d7e6a4da3", "guid": "bfdfe7dc352907fc980b868725387e98fefaeb53c4a5c1605a37f374624770cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3a101014fbcdec833c729cbd17e008", "guid": "bfdfe7dc352907fc980b868725387e98a02f8869906546127c09de7257981fd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f1b367a574b5433c5f62407f3f7fa8c", "guid": "bfdfe7dc352907fc980b868725387e98fb9e9e2b99ec0f63de370565409722fe"}], "guid": "bfdfe7dc352907fc980b868725387e98acfcc08fc13f1fed7fef8f9ff19a4507", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2b723135fa9bbd10994d317451ac895", "guid": "bfdfe7dc352907fc980b868725387e9815745cdfdd01c55946faed7bfb204c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98320aa227f3c993a705f646bed0016061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b01d9da8179879fda9dd6cdcc750946", "guid": "bfdfe7dc352907fc980b868725387e9898800a2a9de50ac233dc98433e093ffc"}], "guid": "bfdfe7dc352907fc980b868725387e98ecd3bdb53bce9f6366cc39176d6481ec", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e9014bf2b3e280f8fdb76a7b00776cf3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ed846bc5edbcc85d935ace19b53742e0", "name": "flutter_sound_core.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
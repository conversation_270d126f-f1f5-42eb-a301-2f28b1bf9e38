{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a49314f45edd5608b910e897ee37948f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d02a147dbe1281466fc93e5a6761dda6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc9b9f200cf36a74dca8ec3e2a4bb300", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98786263286217dd231530407c96bbe97f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc9b9f200cf36a74dca8ec3e2a4bb300", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9848bb7b6b827398b34ebd3ecb8d83e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7b7db296d9a8cf402531edb75dfa262", "guid": "bfdfe7dc352907fc980b868725387e9837c366135f492852611f992eaa4fad37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98389e8751533d337bc6269d27ccad6f6a", "guid": "bfdfe7dc352907fc980b868725387e984b2afcd9011eaa0fb265bf7abe2dffab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d06546ab47d87a2dc5c643ad94e854", "guid": "bfdfe7dc352907fc980b868725387e98dbc5585ec0735389f1ff9b8b6581c077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890d16afce8ecea2e69f67d2f116bc199", "guid": "bfdfe7dc352907fc980b868725387e98af2f07560d93cb6f23a6817acfe8b822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241ae84e393606ef4e352caa15053ef9", "guid": "bfdfe7dc352907fc980b868725387e98681727950c1b62f84d554340640cff67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98467e90d1287e9fae02fae53c3d373be5", "guid": "bfdfe7dc352907fc980b868725387e9871d7271737359e347ae6f68b91694810"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989551aff2159294e9639cc6696361822d", "guid": "bfdfe7dc352907fc980b868725387e98e81b92dc0687a1b8a20ff6a9e5f1023e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2766153eaa9b649b3c0e8f2260fdaa", "guid": "bfdfe7dc352907fc980b868725387e980a5d3931b92030125df2af2b8178cc88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694063a15ada791900d763c8067fb40f", "guid": "bfdfe7dc352907fc980b868725387e98c61f166fa629f3755c0f8a8b3288e31c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982204ad76e90e3c7da2732fcd4927ae54", "guid": "bfdfe7dc352907fc980b868725387e9820d57f62307d30f12d5d9d362306cab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae106da0bc3971ca437708889897f595", "guid": "bfdfe7dc352907fc980b868725387e983257b59b2068e88031eb8d40cbc6c2ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec540ab5bb8889ae9b434fed77db623", "guid": "bfdfe7dc352907fc980b868725387e986677bae3f7ddb146bdaca919e4d1291b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98065a3103124bd048a8205db2c97620da", "guid": "bfdfe7dc352907fc980b868725387e980094ffec9119114b65313f17f9360c26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477f3e9fc46939c81fe1035f30373001", "guid": "bfdfe7dc352907fc980b868725387e98cbea32e23864c33c740b95565505bef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464d1b01febd0bb0234fbf4e5887d059", "guid": "bfdfe7dc352907fc980b868725387e983443f8b820113f0a9327ec916b93c0e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c18bac7a002d6cab58ce69d585e08c", "guid": "bfdfe7dc352907fc980b868725387e98e76585bf7f76bb4597065f257b9a152e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee9a4df5da969f22414d46de013c84d", "guid": "bfdfe7dc352907fc980b868725387e9889116b87c33ac074a5b8fa59b8782c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882f77efcba23370bbe240385545947b5", "guid": "bfdfe7dc352907fc980b868725387e9831bba6234466558dd3eb438579441634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824dfc74ee8e52baf26b692fa4ffc5906", "guid": "bfdfe7dc352907fc980b868725387e98b47e1b8b9755bfc55d47a5a9bb5bf5f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0200e5a52cc5345e27e4b9dc033e24", "guid": "bfdfe7dc352907fc980b868725387e98b0f34eb48501362977fb2dc211c947f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef246d9a4569a4fde8d8f23d65db7c2d", "guid": "bfdfe7dc352907fc980b868725387e98d68bef293b67caa8fc4660dd7d54e445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867984943379cd3764f75b315bb984ba3", "guid": "bfdfe7dc352907fc980b868725387e98937d9212c7c0a3efb55920d573578428"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf9b763ba8c142178fd82e6670301ff3", "guid": "bfdfe7dc352907fc980b868725387e980cf735c05ca28cd4bb1abe33c2a67dae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c3a4e807680b82be8209a63bf2d4edc", "guid": "bfdfe7dc352907fc980b868725387e98d4f5efb05b5911d3fd4849245235aba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f76e57a227ed8e49ee1992f58510de", "guid": "bfdfe7dc352907fc980b868725387e9880a9101abef163f9a657bc0a4c965c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800d006ff3ad72b3eb2b788ddcf765795", "guid": "bfdfe7dc352907fc980b868725387e988d19aceaa3d34d56c0b1ea0e67c40ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98380151d26b879ff620854d256a5fc54c", "guid": "bfdfe7dc352907fc980b868725387e984bcb769a9c249782f450253511cfbff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b53dad798cceb94f22be7df5d1ddbf", "guid": "bfdfe7dc352907fc980b868725387e98381f4a4e7a42f645607fc3a64b2ada35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2be95682eaa4a473e0fabee847e5bb1", "guid": "bfdfe7dc352907fc980b868725387e98d814701ab8678607ae384fc4b7488ee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d25856afa5c14b994cc60e34e1ea002", "guid": "bfdfe7dc352907fc980b868725387e98685be9db260690810e83b7a3882b2a4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c7016f75658d7c8a06a6f476282211", "guid": "bfdfe7dc352907fc980b868725387e98200c29a19d8f5a373019a77e23319c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987713e144a715e1138e2d14f81b1083b6", "guid": "bfdfe7dc352907fc980b868725387e98ac455a54eca23ef6446cac67b1ae330a"}], "guid": "bfdfe7dc352907fc980b868725387e98012295260b7bbfee8caf5c650342498e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9835f25847ac16fe752696b68ef1f62f01", "guid": "bfdfe7dc352907fc980b868725387e98ac4be4f4efbdfc5e1126223a28aef459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c50edbc81e7a0fde5316380a71232b4", "guid": "bfdfe7dc352907fc980b868725387e989fe193e740ee0e1af80ec3aafbbdb1d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a9f1de0a66a3a98f4c510a17aa014f7", "guid": "bfdfe7dc352907fc980b868725387e98b2c5d7f3145a9c70cef3cd8b7a655f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f61b7aedaaf364c049f9f46a5fc2342", "guid": "bfdfe7dc352907fc980b868725387e981b4514eb91d339637fe11172c5c19d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d64751de91b250cbce820e8fa5edd0d", "guid": "bfdfe7dc352907fc980b868725387e980cb89247def27cf80762f240aada728d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1ea9362b37e12e277c5f91294e4078", "guid": "bfdfe7dc352907fc980b868725387e98180d8bd6d03a5739820a99525935dc92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff21be56421fc38b1bd553afd6d9f51", "guid": "bfdfe7dc352907fc980b868725387e988103ebaff9676667de4aff21d835c5d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc5e83d735fd4ee1a5ff35c84fca7e9", "guid": "bfdfe7dc352907fc980b868725387e98a65b4c2eec600079742a64df27efaf04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cfe92f3f3d9a3b68c117b291fc02c9c", "guid": "bfdfe7dc352907fc980b868725387e9877beb3648c7cce111344222970892143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde83b00e6bd842537a3c00d22a45286", "guid": "bfdfe7dc352907fc980b868725387e98ae50830d8e31304c2efede668f2addb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dc115acedd999f11d730359272f8a31", "guid": "bfdfe7dc352907fc980b868725387e98cb0f99fac3af4796bd194aac3380eb75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc9864960e2aa624a0ca08a84cbce38", "guid": "bfdfe7dc352907fc980b868725387e98741e25bd97acaaa332c560d523a37be0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986165e529cc0b2d0b07016eb757a2b0f4", "guid": "bfdfe7dc352907fc980b868725387e98db6ef1c1d3a745b5454600d0360d61ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f231d257e4cfcb1cc33810793c4da565", "guid": "bfdfe7dc352907fc980b868725387e98cf1a95d578f972fca93f0fc9378bd859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a29ac8711f1d8bdaf49014bf5dc2207", "guid": "bfdfe7dc352907fc980b868725387e98d35296e3808cc1e37ec06fb6f167073d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc81091d9e2faecb59b85f6cb8185779", "guid": "bfdfe7dc352907fc980b868725387e98f0dacc9b63803182e673bc0c183faf6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899856615a9af0824c04a781890d5bde7", "guid": "bfdfe7dc352907fc980b868725387e9833d3cc4349783d5bd6b47a97988fcf09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbefa2fb431d21520bd40ac460fa6b60", "guid": "bfdfe7dc352907fc980b868725387e988436c6dc037ffa899e0b461b49acb300"}], "guid": "bfdfe7dc352907fc980b868725387e9834a37b2154c91488bd5232977eb6f26c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98d752e9372f56c852b1f40bf8b46dd1f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f445a9bdfb8126a0334a1f4c6d511e91", "guid": "bfdfe7dc352907fc980b868725387e989d15ec5c8c240b76a3b39ae8558ac52f"}], "guid": "bfdfe7dc352907fc980b868725387e98be972587179df3a2f0f14071bcfbcb38", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98854d1b6c3c514a28422112fc593eac08", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e980754be19a8cca4c81cff402557b68206", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
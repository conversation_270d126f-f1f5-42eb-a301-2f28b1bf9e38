{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896e9e2dfc12d496f8a1c02396b8d15e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810bdb3018dba7b3fc87b085e5c7a18ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873ca2bec2d4fc5a438fbbf65df93f7b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808213667fea3cf0e28f6feb0339126c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873ca2bec2d4fc5a438fbbf65df93f7b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892b119b956a581fc1e399b4ce8515719", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8b2b185aa8de0f04d356ed8e70e0400", "guid": "bfdfe7dc352907fc980b868725387e981f9f44258c781ec7ddc7a7ad246744c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314025c211a7a5d9c5e2e45d81b59f9d", "guid": "bfdfe7dc352907fc980b868725387e9853844dd827deeb74acc7d4bb9ea6f450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98749f015acda823e75f30863f98330dfd", "guid": "bfdfe7dc352907fc980b868725387e98a1d103781e0525ca94272e6bcf4697c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3319f51dd4aba0103bd9fb0735b52a0", "guid": "bfdfe7dc352907fc980b868725387e98efd27c7a91ad8b9bb99705c87254de9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877fa9afa348705e906e163aa3f7da594", "guid": "bfdfe7dc352907fc980b868725387e983cfd86b207c1d9bd0497151df918b555", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac402e4d91d2693deb10fc3fab97783", "guid": "bfdfe7dc352907fc980b868725387e983c19c63667da01d81c58224c077e09a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d932585cf567c4abd19e07cdc2ae5d", "guid": "bfdfe7dc352907fc980b868725387e98d5987ac2bc6df0964faf79850bbaca69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a04f8ec7729401ab38997a66c958534", "guid": "bfdfe7dc352907fc980b868725387e984c1ffaded7cc95897f55befcdea0d101", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e3afb865113f03f6d8901c3c3f131d", "guid": "bfdfe7dc352907fc980b868725387e98d4ea328e428d6f4d742007110cdf4547", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817180fe54e311341e4a06ca2eecd4f2f", "guid": "bfdfe7dc352907fc980b868725387e9807d94f599b0b74648773dbdc799a5673", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e176dce7cc59a28baf62be92feaaacc", "guid": "bfdfe7dc352907fc980b868725387e983c69d54bef715255a2d73b919c076f52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f96c8d10e228ef7cc4d86b060f23a92", "guid": "bfdfe7dc352907fc980b868725387e986e79ac7ae3effe91d95526d7cb93ed32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56f0d7cafad72633fefa00f35adac02", "guid": "bfdfe7dc352907fc980b868725387e98f2e51442dba078058a4c863f85388842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988224a6a314adaa8f3ae5a61807cb8fee", "guid": "bfdfe7dc352907fc980b868725387e98ddeca466515749995b7489bad3ffaa55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfdd7993ba13b18ad5e79c8f9a557516", "guid": "bfdfe7dc352907fc980b868725387e982574dcdb9bac83f8b15c681bc8882b82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d745f1c46d23a406a397aef5c64dfb1f", "guid": "bfdfe7dc352907fc980b868725387e988f6e9ba1957141821046ae8ca6017059", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b83e9fd8242b47cbd940f6081332e5", "guid": "bfdfe7dc352907fc980b868725387e98d524e45eb0491a7871b33cc627fe2e6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eeca4e2067ce3ec223478674ab93609", "guid": "bfdfe7dc352907fc980b868725387e98e467393dabfe01dd87aee790d6d6f24a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14b89cb6422c1cf2d087543536a5db0", "guid": "bfdfe7dc352907fc980b868725387e9854c131e4e6f707a42e94188d345487a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98749585adf3f3656d25aa07162d11a59c", "guid": "bfdfe7dc352907fc980b868725387e98c77034a70b86ca32f65ae80618477d95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e7e7afe2ccb9f0048f1bfbbc55d840d", "guid": "bfdfe7dc352907fc980b868725387e983749d569a93ede26bc8c533b408146bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d09ca8fa11dc02e334b23967f4e831b", "guid": "bfdfe7dc352907fc980b868725387e987b45037e9442e57f91d00449b5c9b5ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857d3a035015d6249e1b275d506e7b51e", "guid": "bfdfe7dc352907fc980b868725387e98b964500204c413e4a44e514021352701", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c137abd9b136397c1aabbb427494e1d4", "guid": "bfdfe7dc352907fc980b868725387e985365eb10ce41af93d3ef9042c796a5bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b809dbfa4c6712e1c19e51590af7fa04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9849e71d766be37c88f612cbe7ea33e284", "guid": "bfdfe7dc352907fc980b868725387e98f0343fc583501ec403d1770633ba88f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a84c6f96860305bcc7a85329b56a1fe", "guid": "bfdfe7dc352907fc980b868725387e989dac354c2c91a4314017aed517eaab24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883dabd0da092fb65351f4df0ac666fe", "guid": "bfdfe7dc352907fc980b868725387e988edd8f913961ce69bcbaf42f0839dd70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c43fba38bbaf517160d80af0cf721a", "guid": "bfdfe7dc352907fc980b868725387e98c77438b5b46ae819137345589b8ab5c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706c4bc824797b8e58ebbe6864f1f892", "guid": "bfdfe7dc352907fc980b868725387e982ce81a05e8a1a906eccc356ac1b99592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6bca2e1d5355f217cee0499cace568e", "guid": "bfdfe7dc352907fc980b868725387e985a737656bcca0427c4eb9c893580524e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8c0506bf70ac70bd1fe2b0c91350c3d", "guid": "bfdfe7dc352907fc980b868725387e98ffb4141887ba137b9f18011c2ac503b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876276f33d3f9c169f43aa776e3f4bfbf", "guid": "bfdfe7dc352907fc980b868725387e984f0f9de192ea70f445650f25ce459475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd826193a20881073f54f0cc21686e30", "guid": "bfdfe7dc352907fc980b868725387e9854991268c78e443dea24d15767cd06e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af39b2cc83b671ff5498cf26af913af", "guid": "bfdfe7dc352907fc980b868725387e98d4cdd1cf65feab22a0c41cae934dda42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972b813cd9550059a35827aeab39576f", "guid": "bfdfe7dc352907fc980b868725387e98388b577ecc1a54ce7f6f065a18b005d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987679d0b3336b1927e0a2606c41f4d7b9", "guid": "bfdfe7dc352907fc980b868725387e98cd2b5b7dffe753842f8001a15e84280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831135ac6424a7d303674f72ce6fc63e4", "guid": "bfdfe7dc352907fc980b868725387e98f5e96517724e4b7df97e1040518412d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984d05d134dfb89c04ed4ea1fa384dd9", "guid": "bfdfe7dc352907fc980b868725387e9813bf86e9fd4a287a174e4dda3aefc396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824b33b7d7de3f27a26a24b833fefc5d", "guid": "bfdfe7dc352907fc980b868725387e98a8df9e905dbf7cdd9b24a42886c62375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f37d675717c87aa8a795deaf6c6ea2e", "guid": "bfdfe7dc352907fc980b868725387e98b1ef489cfdc4e984632a4b953e770890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea622461723f485a96b6a8717a39b09c", "guid": "bfdfe7dc352907fc980b868725387e982ec1f88638126659ec68389ae052e3ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9ec329563fb983a15509d80af536f89", "guid": "bfdfe7dc352907fc980b868725387e98f4c99bb9a3751f4a617249a410285922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54eaa8b813ac2c9ada9d78eb767689d", "guid": "bfdfe7dc352907fc980b868725387e98b5135333072e7fa703ad9656c98f6265"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a5ecde5d8bf2fdbf753931660512bf", "guid": "bfdfe7dc352907fc980b868725387e98b0bc0fca338416d8f31506967e255756"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98722819ccd04accc4b1073b1c672c3039", "guid": "bfdfe7dc352907fc980b868725387e9849aacf2ae5e7e1198f7591eaaeb604de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e560be3e050dfdb85c0549465dd5a9", "guid": "bfdfe7dc352907fc980b868725387e987ae6694d3d8e1e744a7f4d80aedb30d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce230aa14ed651ada42a5b7281bfd691", "guid": "bfdfe7dc352907fc980b868725387e989980e3343a39ecfb8cc0522bda25f465"}], "guid": "bfdfe7dc352907fc980b868725387e98295a6a912ea2f996619108451429afc3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98d69d3f754ff2570528c4de0715d27ea9"}], "guid": "bfdfe7dc352907fc980b868725387e986ff48b1d20b26e526e96f1291209d3a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ca04b2f98dda4aa3ea7d695df2288e55", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98b319dc9b9bc94b538d198b41cff1b6d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
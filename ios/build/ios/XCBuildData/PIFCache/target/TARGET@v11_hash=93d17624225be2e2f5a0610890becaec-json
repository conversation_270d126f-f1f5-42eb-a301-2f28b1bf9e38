{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb8b69fd69e1402341aa66a35e227247", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9869c85e9db9b00583fd6afea24696c4b4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884b76215574856bb6974a0f71ae8bac3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d650b3ffd71d8e20d284fdba6f5b3404", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884b76215574856bb6974a0f71ae8bac3", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987f7e9e300a002c1f9c39d351ea11da87", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e74fc440ed07a3312f75716455117689", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9836607365d8f9a43019c1652372e7199d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983e442651187cdff2bb9a6338077d5804", "guid": "bfdfe7dc352907fc980b868725387e98ded8b217a5e93265db3ac835d62d9e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98826c7245e3a902d9d241b1f0a4942402", "guid": "bfdfe7dc352907fc980b868725387e982f28454ca96d8b47e65da33e54a6910b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e35593c964d20e93ce9f945eb4a24197", "guid": "bfdfe7dc352907fc980b868725387e9807d86ba0cbf94189191b5b7f1930345c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df56cb175be798043217d84d0f80c61", "guid": "bfdfe7dc352907fc980b868725387e98c272086661dd1cb6c5f50bf5db3cd622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ab6bf3da1244f98ba2762640f0a1925", "guid": "bfdfe7dc352907fc980b868725387e98760c290ea3cbd421c9aed79c69d273ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9c2772fdea509e840074ea0cbeee2dd", "guid": "bfdfe7dc352907fc980b868725387e98b4aa1b8208b9e3d376b1184340acd30c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c1987ec0955a6a951fd8069806b603", "guid": "bfdfe7dc352907fc980b868725387e98002bfc8cbb6bdce22b464b8d69885507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c864edf7f7a538417da07d10667dd92", "guid": "bfdfe7dc352907fc980b868725387e9835506ffc35d55315786893a70ba9beb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f4c89bda83ab4eec8603751855798f", "guid": "bfdfe7dc352907fc980b868725387e9871d665f6291153d309cd3e211815efce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986974a34df950f558d42fa771d8186a6d", "guid": "bfdfe7dc352907fc980b868725387e98fa51e7f4039fc4ec56f76c7e80dba7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988011158d8508dad7f7f9eb88fda17f40", "guid": "bfdfe7dc352907fc980b868725387e987842e28081a0bd947d0949b582188581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cbe1c7c5856619d92b05ea140b18eaf", "guid": "bfdfe7dc352907fc980b868725387e9831d82a81b8f896862e384947320261cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98585b4c464cae87dbbc85ff0b45d2e717", "guid": "bfdfe7dc352907fc980b868725387e98192f14671500431e94d998702eba9683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aaa77db1984aeb06c7380fe6b26311a", "guid": "bfdfe7dc352907fc980b868725387e980df85873b33822623e4ca0fb1e797d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62ded4090e22d99299564fce6116669", "guid": "bfdfe7dc352907fc980b868725387e98c4ff4e771f3431c8cf26b5dd5809f367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac888f2d485cb98a90769a40b9d42fb", "guid": "bfdfe7dc352907fc980b868725387e9840539823a9e60ce29c4acfba283e7278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d57b3f38c078c633f9ee5f93203fbb49", "guid": "bfdfe7dc352907fc980b868725387e98dbbf0bf49afeca48096a160aaf3a19c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa9e15178e84ed30634e8eb2e0c0b1b7", "guid": "bfdfe7dc352907fc980b868725387e980c86c248471dc5c60ba432ce9d2b628f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98699b65adc06957a0a40f935c457988dc", "guid": "bfdfe7dc352907fc980b868725387e9894bf9dc3f401c23717ce32e54ccac54d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae4520de9026ec5da5a1d1c582db268", "guid": "bfdfe7dc352907fc980b868725387e984ba5aa086602a40eb4123b4323ba46be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8e463dd447e25cd0e16fcd10aab0070", "guid": "bfdfe7dc352907fc980b868725387e98a1caf37f1f945dfa2cc52fa9e237a449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e4d8f31d2fa6c464cc2fa8f6f7e49e", "guid": "bfdfe7dc352907fc980b868725387e98f590c94fd2ac4821c8ed3f279983056f"}], "guid": "bfdfe7dc352907fc980b868725387e98a356dcb4018fd72d070de5b0c6538aa8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
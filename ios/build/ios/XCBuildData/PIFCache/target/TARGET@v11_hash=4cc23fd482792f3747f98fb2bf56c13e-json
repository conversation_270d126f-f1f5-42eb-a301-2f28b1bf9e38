{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4b2aade0b122ef93564e5bee0809b42", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5e069c079f73f1d2c54f9865415d3b4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c223370a52f8d29100388a1a424d2ea8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d901cd1db98ecc0e9eddcd2466173866", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c223370a52f8d29100388a1a424d2ea8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e0574b788adc847768f82e3f4cfe377d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee53937c063d78e79479444fa6d9cd4f", "guid": "bfdfe7dc352907fc980b868725387e98cd9c503171a5097ba8582ce8a42c98c4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985b3ab0bd64db8ea6e3ac173ee0b7ea6f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888d285ccd99819a5a9d1725ab667ee92", "guid": "bfdfe7dc352907fc980b868725387e9848b77ae81dc767d4ec8f6285eca188a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847def1c798afd52be6b8833d436d781e", "guid": "bfdfe7dc352907fc980b868725387e98995dbaf3fced7ba214a4526469b9f7ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850d63eea892600fdde2c0a0fa78537f6", "guid": "bfdfe7dc352907fc980b868725387e980b6f9bd87a737f395f943b8af54a88e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2ed9e02b5613baa523a512d4e88cf0", "guid": "bfdfe7dc352907fc980b868725387e9827cd7189919d2e6b39ba75ebaede8df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c0ac99562144e76dae63a66b24fe41b", "guid": "bfdfe7dc352907fc980b868725387e983935a1b9bea2d556be977267524aa8f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5357bfec4b904957a13bb52b3916a98", "guid": "bfdfe7dc352907fc980b868725387e9817300941eb2a4091f84bbf48a46f2d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f190c35078a4cd1ba870f499207cbd7c", "guid": "bfdfe7dc352907fc980b868725387e98d3ccfce2db0b6fced26f4914e41f04d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1e20ed668535207209b2d9093cb3c1", "guid": "bfdfe7dc352907fc980b868725387e98fda572b9dd4f1c95aa32d3edd2d899a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e306cf99e4aa86922b95cf99c9badd7", "guid": "bfdfe7dc352907fc980b868725387e983898cf45a297988da119abd37c04abbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282d6538fe31b48e2a75cb38b93771f9", "guid": "bfdfe7dc352907fc980b868725387e98c300c329537ea02549b445c228189452"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eec05d8fa9065572418c184e0407874", "guid": "bfdfe7dc352907fc980b868725387e987a84cf47d0a498987d50056e19539696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ce71726d85f370384646672b2b30ee", "guid": "bfdfe7dc352907fc980b868725387e985c70b7a383a039e2f877588d33ee8e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e5c7f54809f9ec5c3f4c6dbe7122e2", "guid": "bfdfe7dc352907fc980b868725387e987add137ae288cbfc5f02f42b6861af39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ce1e41c9cfd2b9ec994ba965172d23", "guid": "bfdfe7dc352907fc980b868725387e9836785dbede5a1637a1838bdfa7aa028f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464345e1f4493f431db4296cb797d822", "guid": "bfdfe7dc352907fc980b868725387e98dc3dae863ea6ad44579c2622e86fda21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9c05f47b0f09707ccff6c1534cf7561", "guid": "bfdfe7dc352907fc980b868725387e983e3c0c872ef33e83c4020f35c853ba23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881a69675f820e40eb0211f9c45f87866", "guid": "bfdfe7dc352907fc980b868725387e981ec79f96cb3512093cdfb5d81e0d041e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d38a58e0975c5acaffae8add3763722a", "guid": "bfdfe7dc352907fc980b868725387e98c888ca470c2b467fa54dc7e9b1c2a62b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fdc943041bdf6703b7cbef1a8a641a", "guid": "bfdfe7dc352907fc980b868725387e98fa82aa4ba68f6285cc6b726a3400bcc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827944d5ae8a281877418aca0e197ee89", "guid": "bfdfe7dc352907fc980b868725387e98b83285e938c960160da4330687468fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d893f94a61455fde6ff6def0c7729d", "guid": "bfdfe7dc352907fc980b868725387e989d114c8a359a1643c3be2bf6d3ce9302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982261d8bbddfbd579fbae42810ff99050", "guid": "bfdfe7dc352907fc980b868725387e988d57733ef70b045aaecf873efaaa6e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859b989156bb9f93a43d74bd1c22a4196", "guid": "bfdfe7dc352907fc980b868725387e983f9f1ba6297d6c14fffc567ce26a3645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba12feb82677a25b92fe1d16316d7def", "guid": "bfdfe7dc352907fc980b868725387e98bd1e0d1c2ed3dc74417cc8ca6a67e2cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3792e81b8cdb7296194a4440ebc9dc", "guid": "bfdfe7dc352907fc980b868725387e980696fd5f75ee8686c9f6da9647cfc19d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8417646ae1b1055fb761a3abd3b2112", "guid": "bfdfe7dc352907fc980b868725387e98fce45035881412f75c0d8ffe32637a48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d677df6bbd2261ddbc1eb125481933be", "guid": "bfdfe7dc352907fc980b868725387e988653763d250bb08fb579f473da51825d"}], "guid": "bfdfe7dc352907fc980b868725387e9802d94cad7c94b67392d64ff4c541d3fe", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2b723135fa9bbd10994d317451ac895", "guid": "bfdfe7dc352907fc980b868725387e9814bd0893073b4daaa0d9d273d87c4738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980991f77c560012bbd6a11e20f85fbb3e", "guid": "bfdfe7dc352907fc980b868725387e984c7442109b344c0e34c97f8d16ac0aea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98165d69e99abf151586f8e0a4acf3cb2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b1151a9e67c049ca918b0ec54ed5aa", "guid": "bfdfe7dc352907fc980b868725387e9841bc761e2332a84f4e4ad73f0443c021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcbfcafc62f67bfc05776d7c770a69d", "guid": "bfdfe7dc352907fc980b868725387e984a38aea8ea5184ffd8f8482cd5c0807d"}], "guid": "bfdfe7dc352907fc980b868725387e9801aa4c3d8a17e1852cca085bfb0559ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cf989ce35b9d5521d2872313f10c56af", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98313c62dbd91120773dfab4dd76935217", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
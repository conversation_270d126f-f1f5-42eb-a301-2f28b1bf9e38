{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb8b69fd69e1402341aa66a35e227247", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cec2ec1f80a7da89a4fdac18efc6203", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884b76215574856bb6974a0f71ae8bac3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea7be7f0530eb05523950bb1077f4a39", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884b76215574856bb6974a0f71ae8bac3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f36e7195baa9f7e8f5a409e75d2fa8a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985bdfc724ea86c8826cc89ebba91b6421", "guid": "bfdfe7dc352907fc980b868725387e9861bfbb6a236bfafe99d24647fb0a648c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fc56ed640800de61cfb79a991736621c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988559e588ec8b13440edd853c047d9f14", "guid": "bfdfe7dc352907fc980b868725387e98bb4fdcc38b0c6de8a84737aad1b2ea91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857c2d7b9dc1f3f4a0c3273b3909ce230", "guid": "bfdfe7dc352907fc980b868725387e98bd404fab034d291ec40154ea90aef255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983694c081094ca7e4b186716cedebb01c", "guid": "bfdfe7dc352907fc980b868725387e9855cf4237feae7d230b5b9f5835b27865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5fba01bd275ff850eb3b42972f3e5f1", "guid": "bfdfe7dc352907fc980b868725387e98fe96ca541e805510716f76e39959b092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bcd819fb8e095c0cf7783f22b25a9ef", "guid": "bfdfe7dc352907fc980b868725387e98c77eeb5a39dcdd516cf5a5e59147a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869cdd4e844104e7cdcdde584306a92d7", "guid": "bfdfe7dc352907fc980b868725387e98dc10d91995175f85a161b906f4df6c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72cb51605b6c521a2354e6d9a1fc7bb", "guid": "bfdfe7dc352907fc980b868725387e98024b4b959ad8c3b801cf226d53d4cfcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422df8063227c55850f79ccf253f874c", "guid": "bfdfe7dc352907fc980b868725387e9803adff81be901d1ae24761baa2eb7059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a190c03579944071f0d935a9e72ce129", "guid": "bfdfe7dc352907fc980b868725387e9800782d6d178d0f4ef6a7599725678f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2abdc84fc8dd2e72881c269f8ddd8c4", "guid": "bfdfe7dc352907fc980b868725387e984e5f8239733b87e173492ead11e18d2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f19105d110d63dcaaa72e8ad0d2ffc0", "guid": "bfdfe7dc352907fc980b868725387e989a0dcecfad2408dcb45a15a20942c7b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814350e0d7e027f78e5ad5af0d2934f1a", "guid": "bfdfe7dc352907fc980b868725387e98dcb29b32065be49fe2d45e7c1a384350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eabe9a3dccdba9d343c87e2334ba158f", "guid": "bfdfe7dc352907fc980b868725387e98b3be41c4d4cb57acd97dcbf9056a2034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987230c07838d01907b9efa46a5e76c4c4", "guid": "bfdfe7dc352907fc980b868725387e98c44ed1c7ef2e335edf03a8669e500384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd0821d08e13f0d92c7152b08efeed27", "guid": "bfdfe7dc352907fc980b868725387e980b6bf23598179d1b460c191ca0bdfd2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea2b6680fcb3da29d73c3b053ae1e32", "guid": "bfdfe7dc352907fc980b868725387e98987c9a52fd9f1cc6753bcb32a03fd2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f6b0e313f7dce2000e35b41049713b0", "guid": "bfdfe7dc352907fc980b868725387e9805ca1257218fc06bf362dfc8d328f733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d97ed848cd72537b66040ed5f1de6b5", "guid": "bfdfe7dc352907fc980b868725387e98a242a252d282ca671d43e04129c0b161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452f02e604a4312b455b35dacd706ef6", "guid": "bfdfe7dc352907fc980b868725387e986ebeab8c8272f8dbe0e8c1a26eb6361f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dadb642188497429960cf39b1a71e48", "guid": "bfdfe7dc352907fc980b868725387e9894ada70d33f11d628f5aa19102cc4e4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe5a6379335edd7c1e745a5e0078dc9", "guid": "bfdfe7dc352907fc980b868725387e98612d965d7a7fce750c679b8ccd3786dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225564d288449f79f464fa6d0a74ce5d", "guid": "bfdfe7dc352907fc980b868725387e98b8df2e2d67946993fb61209d54b1649c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982067a15cad2c80d4282ff6ae545dd132", "guid": "bfdfe7dc352907fc980b868725387e98a82bf5a58c3069f0b7cca6767ba71341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19289959d8a97b19bc466702a574899", "guid": "bfdfe7dc352907fc980b868725387e986b453f530cac7b3ebdcc574b6c60fdbc"}], "guid": "bfdfe7dc352907fc980b868725387e98212737d6db54fb0517c3224029e3afb5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98038249339b7dff72c7c598ebba6b91b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b1151a9e67c049ca918b0ec54ed5aa", "guid": "bfdfe7dc352907fc980b868725387e98cb5ca1666ae9dae6138b4306c79a51ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcbfcafc62f67bfc05776d7c770a69d", "guid": "bfdfe7dc352907fc980b868725387e986e39e0bbfbf7b24fece993779bb585ab"}], "guid": "bfdfe7dc352907fc980b868725387e98130c36ab2480349ba5ff0262c3b45df3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e6fff4f6cfc8d12ebc57c4bf8981d65d", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e98b4b5611bdd6ff9f5661a6dca82b1072e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
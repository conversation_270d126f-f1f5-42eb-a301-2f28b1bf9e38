{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98681835ee153828ba989d4ae55db90db1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292fa1a2e602e72cbae03ca4381d0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988212539290f209f206b74674b0e7696b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c2c7bb83ef85df2b5738875e12a3b9e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988212539290f209f206b74674b0e7696b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802786e96202a989faf8737401d76d959", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874524e1b7ba4ed8a6e3a81f6592662eb", "guid": "bfdfe7dc352907fc980b868725387e98e37596068807202609d79cb2ba55aed5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd153725084515b8cbbf2d9c86214d27", "guid": "bfdfe7dc352907fc980b868725387e98be59d80031154029e57d3360e00e5280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c68d81be65b55d0f03900567622698df", "guid": "bfdfe7dc352907fc980b868725387e986fcea6ce40731861a2d3e2f63212e1eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc1aeb64b258ccf6a5ca4c011b5d75eb", "guid": "bfdfe7dc352907fc980b868725387e98cdf92bcf569c0cc7987d1a5ad55eccb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e303621efed7c97cb0e3cc36ca66efe2", "guid": "bfdfe7dc352907fc980b868725387e98895bffebcc3a88e76eb205ff859a63f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ff2548c7d72568340ab38b0f696642", "guid": "bfdfe7dc352907fc980b868725387e9898823c224c568839bcd5896994522a1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986324ec5bde7d9bf646dbd1da8fc350ef", "guid": "bfdfe7dc352907fc980b868725387e98d1eb46b50884bf6b974ba0a74f36a1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cafd8f10833d764ce9664ff0389b479", "guid": "bfdfe7dc352907fc980b868725387e98005cc2d69f87b51d122b6757f6741aef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98170be90069ea09a3d2a137b27e45bafb", "guid": "bfdfe7dc352907fc980b868725387e9851e4d5034db6b3dd2be92bdbd0ef6308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ffbc36c997afe4a07d6f7da4da756bd", "guid": "bfdfe7dc352907fc980b868725387e9804035e4ab41eacc7e3eab1663ce4bff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98647654caa325d1920a8d545179df6aaf", "guid": "bfdfe7dc352907fc980b868725387e98480d7357c34813b42d4fc782aaf91161", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0595b26ab5c3fbc18160d31a6bd906", "guid": "bfdfe7dc352907fc980b868725387e98c2bc9cb9ba1e33777794f6756966a166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987661cf8a3c302cfaaf2098c7c3a74b71", "guid": "bfdfe7dc352907fc980b868725387e98e43cb36ec9472409b150eaf0f1e7841c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b10a000311c17c574ddfa71083b7987c", "guid": "bfdfe7dc352907fc980b868725387e9854816c3c8488e456759f0f8a9a78f0e8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9732a048f02ac03affb4b4fa7b710bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983bb0c2f4c7174bcabe56ebe157956fed", "guid": "bfdfe7dc352907fc980b868725387e98db6db7db22361c5e7722392e4da662e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3f1db51b22b0ad8486570754b10325", "guid": "bfdfe7dc352907fc980b868725387e98c6a5c53b2c89c6ca6ac15878c186b243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827be1dcbd15919ecfeae781dabcd2aec", "guid": "bfdfe7dc352907fc980b868725387e98efed768a08ea360c692e14748c90443c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3b91ec0f6495234a3392a372c5a905", "guid": "bfdfe7dc352907fc980b868725387e981e2cff2b5d94dc780f5a5e36abd61ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98907d7afc0044f200f4f6939def04a5b0", "guid": "bfdfe7dc352907fc980b868725387e98afc7e9ee791e56334cc1e93034e662a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a0165a911996327ce9784ad8961a73", "guid": "bfdfe7dc352907fc980b868725387e98a2f8f00ec55801f3a351c372cb45dc84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62e82545f633a784049abe3a350efb4", "guid": "bfdfe7dc352907fc980b868725387e98376eb8f375a2bb02f17d2d62f4548022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983921752b3507dbb4a2be2f039fe6491a", "guid": "bfdfe7dc352907fc980b868725387e98eae17b55e57dd777d175d978bef84ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98101ba8e4f73718de17d6c90a7c2cd5c8", "guid": "bfdfe7dc352907fc980b868725387e9862d56b1b0e1652e83e6f892dfdd7ac25"}], "guid": "bfdfe7dc352907fc980b868725387e98964da8c41b24636d9d338de21fb08fe2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98015c062afdb30ba698f98b6ea63a1d36"}], "guid": "bfdfe7dc352907fc980b868725387e9856f337a0270b8ff9a09062fba0a9859f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98301a0f773483e7a62c7a59d2ce137d3c", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e9877cd28b09d57fbb5f9abe0ec343ccf66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
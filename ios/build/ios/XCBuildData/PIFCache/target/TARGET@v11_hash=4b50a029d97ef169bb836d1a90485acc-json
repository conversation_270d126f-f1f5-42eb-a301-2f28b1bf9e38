{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98099dc0a5f3c660eaf7b19f0865cf436a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988acfa9151ff4136c05ef2ba237eb2fc1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988acfa9151ff4136c05ef2ba237eb2fc1", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a331c5cee362632e8bec3e351ba5ce76", "guid": "bfdfe7dc352907fc980b868725387e9823a566c0fb4932618d09806842bd8fa8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c266a9f8a48dfd17dde31d22d94cb7", "guid": "bfdfe7dc352907fc980b868725387e98b69ab94dc327bdaa124ad0df60b2e469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc41f05d0f013e8ca9e452091863fc9", "guid": "bfdfe7dc352907fc980b868725387e98526fd9a56a6d8b5f1e1d2d8e078e7325", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd96f902d022b738516a54cd208b4b5d", "guid": "bfdfe7dc352907fc980b868725387e9812dc2ba830aa459a1786d5afb7f9145d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985440aaedfe8f12639684205b8807626f", "guid": "bfdfe7dc352907fc980b868725387e988d24455ba0bbe22b30f6934c0479f31b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa816dadbc191a7814c5c631fdc99406", "guid": "bfdfe7dc352907fc980b868725387e98d05c4eb0eef34807c573c019ac9c7306", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d84eecd2821848d3848a83d56216ecb", "guid": "bfdfe7dc352907fc980b868725387e980ac645aecfa7d56bf774151481671c44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877dbfb05a9ff191affdbfa42628f4958", "guid": "bfdfe7dc352907fc980b868725387e9851a8614fb37727c647c9bbc2bf56f613", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d63e5e38c6c8dbe02b5d61a28e32be5", "guid": "bfdfe7dc352907fc980b868725387e98a611b1776db17270352e63946b2a2896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c970d8898c362faf9d7dbab45eee1c3b", "guid": "bfdfe7dc352907fc980b868725387e98a0c3659abb02c9da85780654e8b7b466", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ca8bbad3a5c832ea99cfa6be2a4228", "guid": "bfdfe7dc352907fc980b868725387e98e6f77e0becfa48c52c2a6d33b1bb5f5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9083bd5b1a74b39238fb37cd5131aa", "guid": "bfdfe7dc352907fc980b868725387e9856c9181584bba6b0efa166396c4d4772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd24a6533b631b343e21ff3e6076cec7", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497394033af529945b7f93d396d16437", "guid": "bfdfe7dc352907fc980b868725387e987c1c22b2c71348339937dabb03a0445e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1bf597b5a257c378bf66a6d3be8da9", "guid": "bfdfe7dc352907fc980b868725387e987e3f911e4696043fac2d3cbfc833d56f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987129cae86def314d1b00db53b9422387", "guid": "bfdfe7dc352907fc980b868725387e984be6e356a9d81b5f44d6771562eb4ed2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc2d58ff64885980be9014b829a7bc0", "guid": "bfdfe7dc352907fc980b868725387e987a911db4c73fc0bf65a368af4c911ad1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ad649140103d302b1ad9fa2dc0c7ae5", "guid": "bfdfe7dc352907fc980b868725387e98e81e4c99274042f33005ee0bbab7e7af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848cf5fa9075dfc224e196094c2e4ef78", "guid": "bfdfe7dc352907fc980b868725387e984c3d3b6b961259df9831075e8294740e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc10c7550ea2a40066cc38101e7d32f", "guid": "bfdfe7dc352907fc980b868725387e98fbdddeddc77b031ec942b3e884236b33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98600ff7b7f066ad1b2e833241753c8ea9", "guid": "bfdfe7dc352907fc980b868725387e984bbb600184799aeb2473093f7909b832", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c877a17a3e3d20d4f466a9c11a285eaa", "guid": "bfdfe7dc352907fc980b868725387e9869eb624929a483b73f9747ce30c4c034", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ded7a371b1ec82c5a62e0a26e7f985ab", "guid": "bfdfe7dc352907fc980b868725387e98ac90cea72506b82235fb7db55d7750a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e137a63ced7d1fb86425c14e575021fa", "guid": "bfdfe7dc352907fc980b868725387e98dd550edceae514901424823bc492ca93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70cba86ae33859047154628ab113bdb", "guid": "bfdfe7dc352907fc980b868725387e98eda3cf8aa6f0a902f739bb8d01bbc0af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847c60d814df2e672ca6ea356173aa13d", "guid": "bfdfe7dc352907fc980b868725387e98fe4c4c6fe45b81940d91beab38e9474f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882e914ee8283194efa3f972e96e34be0", "guid": "bfdfe7dc352907fc980b868725387e985e8917d28877be3adfed4ba3f3c5ea35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98810a7220e6954927815475c979fdb48c", "guid": "bfdfe7dc352907fc980b868725387e98b586a9594c1f1bf91f8af12c75eb6f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a227322453fe0c45d1f512f980b1da30", "guid": "bfdfe7dc352907fc980b868725387e983457b477f35d9f43ea0cee2bd96924fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98793b0d4b41e59b253459636c8410f15b", "guid": "bfdfe7dc352907fc980b868725387e980f4335528dc7dc8088c9aeae855a9974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98826e984a9541af17d3c55dd6aff20efa", "guid": "bfdfe7dc352907fc980b868725387e98c0b05f1c17ce6895fd84974b68f1628d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a3bd463c9d95471118157d93196fc2", "guid": "bfdfe7dc352907fc980b868725387e983deb1c78d5407c1e5e44424ba7188aea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f49c7041f84b467abbf74d8cef345ae", "guid": "bfdfe7dc352907fc980b868725387e9878d7397ae96ee487e7dcfe97069ee4f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b09d2f12eacd8542d80fccc0dc4f5a", "guid": "bfdfe7dc352907fc980b868725387e980b05da3f4161d90ed1ca55cbad4bd4a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1aac801c5141d8f9b7641d17317fda5", "guid": "bfdfe7dc352907fc980b868725387e9810d873edeb9eb71cd4cacbd32c12a0e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832f8a7cf0921efbce85fe093909c61f6", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848572b3833c3a1854ce693e118cb45be", "guid": "bfdfe7dc352907fc980b868725387e98bbd3fb5413657fd0dd936cca3021fb5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b4054dd3c200e5201c10842b5ff461a", "guid": "bfdfe7dc352907fc980b868725387e98c0e584d3ddb4a99d65306228c599aa3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4233bbaf12620a825e82d7e2e2290bc", "guid": "bfdfe7dc352907fc980b868725387e988184a50f61720a9b1c9b5b671ece08e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987871a6b72926c09eb9320deb16e21fb8", "guid": "bfdfe7dc352907fc980b868725387e983ac4819f373a108b7c514bd7aa7e959d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca3aa73bf6d712f89f22a0f35760981", "guid": "bfdfe7dc352907fc980b868725387e983ccbffdd5493ee09b6329ef5cf898254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb77947a0c3d261af9250142ed55336a", "guid": "bfdfe7dc352907fc980b868725387e98f9ac9dddd93cb10ce8ffb84ac77a50eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a55fdbf41ca27ffca9b5bb12e7f917b", "guid": "bfdfe7dc352907fc980b868725387e985552ae58df1a3572dc408242bb031bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b69970fe7cc2a69b51ce8e043c47c04", "guid": "bfdfe7dc352907fc980b868725387e986f1f1d45a247af3c841978613b00c820"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
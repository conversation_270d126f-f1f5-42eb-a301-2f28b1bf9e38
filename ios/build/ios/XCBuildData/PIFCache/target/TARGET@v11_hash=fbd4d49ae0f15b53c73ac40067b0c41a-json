{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832670c78b66b926c807be8d05601bff6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961d77490cdca416f5bdae1a1e7b08ac", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981dfc081133e351b6e7d98aa0327cdb4d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c297a0c8471baf0f29e39375927781f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981dfc081133e351b6e7d98aa0327cdb4d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981527f6f587fc380cad048142fe43d36e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832ac41004dd24974184419451a3c0fec", "guid": "bfdfe7dc352907fc980b868725387e9839b5ee1b44d10615f8e621c81f551da5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868943d6b6122cae665d3df7f4c1f38b9", "guid": "bfdfe7dc352907fc980b868725387e9830aa4f46ffec2e9642cd84a322fcb273", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed56e55f8d568f6d10e4a41f49c40381", "guid": "bfdfe7dc352907fc980b868725387e98a5a43090203f8090220b6692012beee0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630c8c5a88b280ae544414f3cc96de28", "guid": "bfdfe7dc352907fc980b868725387e98047a9d305c9d176cdd9b30064b32e0ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891d2c1e2a9e58229d63d4dc44982ea54", "guid": "bfdfe7dc352907fc980b868725387e98cb94229964c96919c89f0bdfb933977d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e161ae7199ba335642e8afe3c1ce5691", "guid": "bfdfe7dc352907fc980b868725387e98fbbfaae140e6b700cd515d833f57a1e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b9602486e9590adc0805485d2d3417", "guid": "bfdfe7dc352907fc980b868725387e9853216587b08f801b29afd5ed9673813f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ba1dab3e6cb9b68a5973f0bfb78aa89", "guid": "bfdfe7dc352907fc980b868725387e98ce27e10685a072e1cb0cea9ce4c5e3e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c60fd55a13057ab56aaa5d1bc72364eb", "guid": "bfdfe7dc352907fc980b868725387e9888ce179ccfcce8b3157e919553429d34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5546cf6173a632bd492ab142b159566", "guid": "bfdfe7dc352907fc980b868725387e983efc89c0fc800df13193d3ef4dfe1c33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9fd71d50efaa6f9f9a061a4c75827d2", "guid": "bfdfe7dc352907fc980b868725387e98aac9419d89a6e78e1bfecc52f0ad5751", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c062b250d4f19eec1b5483d279e17c", "guid": "bfdfe7dc352907fc980b868725387e981c0813c8e1d5d1309c06cd5f198d3525", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3d158e055fbc1a86bff90afdbffcec", "guid": "bfdfe7dc352907fc980b868725387e982b3747cb7f50aa5554ae9186debfaa80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaf5ed9d6c2b46e5df2d7d255bfadfc3", "guid": "bfdfe7dc352907fc980b868725387e98045325e4631ddabf995252b1c7bb0497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dd5d4c654392bee7e80897cdb53154d", "guid": "bfdfe7dc352907fc980b868725387e98ab2cdab01717b83d31b7a547f2a76837", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df76eb375b516e18aaaa52c6fce13518", "guid": "bfdfe7dc352907fc980b868725387e98d540296722f79b1361ef655dda9cc36d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb0824940673f29ebd0b8fc17702fde", "guid": "bfdfe7dc352907fc980b868725387e98ea88c690029ccb83a0749de4350c4e7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f222493255f60b077a50f5008ee16d0", "guid": "bfdfe7dc352907fc980b868725387e98a709788ea47982a907a3ed06b3d29c36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad4c354d18afd959c478bfd75543739", "guid": "bfdfe7dc352907fc980b868725387e9870b6b8a7971d737598fc71278491cece", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf45b1383680109c8f10c1963d486d6", "guid": "bfdfe7dc352907fc980b868725387e9871587d3aa6bcabb7f955b60af6546326", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985782c5218d962f3b4fa4c85e86e05799", "guid": "bfdfe7dc352907fc980b868725387e98714a8fa6555a9f419aac163e5ee2539e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590f72bda4ec428cf6efdcb457759ab6", "guid": "bfdfe7dc352907fc980b868725387e98816289278da40941da0db3539c2aac07", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c70ab850cc51a773a13f67e6bc6b6b75", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c73e2e2798c0688a690692acc22ad9c", "guid": "bfdfe7dc352907fc980b868725387e98bd75e7fccd78cbaf595e80951c2fe0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bdcc1903f618e89b9dddde49a16758", "guid": "bfdfe7dc352907fc980b868725387e98c8ce110d82978751520b80446e859dfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d124b3e4b82224cef3b55331a19a8cc", "guid": "bfdfe7dc352907fc980b868725387e98b2b9aa05014239e0cbfa59bb74864bf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8b990a12515b4a603e59f811127420", "guid": "bfdfe7dc352907fc980b868725387e982becb84ea8d508a6a9930827e53b01b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98231cb59b519e601befb3007ad29a29db", "guid": "bfdfe7dc352907fc980b868725387e98917482a35ae846c5805a6185dfe84ed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57c614f3c757365168e812da9d98903", "guid": "bfdfe7dc352907fc980b868725387e983a953b19d83f6076b53cdda746f8d2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb35b05c5057bd7938052f9285e4fc9d", "guid": "bfdfe7dc352907fc980b868725387e98a0605d56693a5ef300811bb2de0776d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98158f17a543408cb39697bfccef66b5bf", "guid": "bfdfe7dc352907fc980b868725387e98dadc4b354681a13016a777523068806d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b13b75abe35c77cca151c083811a07", "guid": "bfdfe7dc352907fc980b868725387e98a5ec1e6e9db158ca938d09b8f8d59cfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad90c12885b4de75c8bc66312d055a6a", "guid": "bfdfe7dc352907fc980b868725387e981c4e17635c1b9c8ac65b414d6d4aa6f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c97411b590d92ec7687378395162ab7", "guid": "bfdfe7dc352907fc980b868725387e98ad2598e2e20ea06bc20a851f5688b6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2fe519be6537b0679b8d67d7df629ee", "guid": "bfdfe7dc352907fc980b868725387e98ed32ed3d9f6d7c8c855c157990291b26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffca718bed84b1b63218ecf293cb197f", "guid": "bfdfe7dc352907fc980b868725387e9866211b8a8bd791f03a9b9f04d27761f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c23cb987a55f51d37f04fb27471745", "guid": "bfdfe7dc352907fc980b868725387e98d2a4e8049cefe014b73cdbec5055166d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39d7832714e27a80ecb864106d8bc12", "guid": "bfdfe7dc352907fc980b868725387e985fc56aeb02e7a9ec97cb6862fbaff5d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc4089b5d4adbe88c290f4023b6306e8", "guid": "bfdfe7dc352907fc980b868725387e98e7c5c1f265098fb0609568e086786a60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b4a60bc677f0cc7671913a57b60c4d", "guid": "bfdfe7dc352907fc980b868725387e987bdcc8dbda564de916e563e30d284007"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827001a86626a486e7bbd231a7fc72fb9", "guid": "bfdfe7dc352907fc980b868725387e9884d5fbd97a9f6b421c2e4194f78e659c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426b01d73ecce2d95c3876598d75b4cc", "guid": "bfdfe7dc352907fc980b868725387e98b2866d3fbd8f031c734ce8b83f709479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0101b7314c47effcd4808200f58aaf", "guid": "bfdfe7dc352907fc980b868725387e98e1c14c55c3b4c4946c410bd837e64f17"}], "guid": "bfdfe7dc352907fc980b868725387e989402769c439d37e7eabcabf5f528dbde", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98c1094772b95db8ffe1ad537f73060aaa"}], "guid": "bfdfe7dc352907fc980b868725387e98e6ae8c4e586f50f448e683b32637300c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98da6bbbc8f5cc710ba10b2370fbcea899", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9850fa5a38fbbe7d1a3ceb965b7c3ca942", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
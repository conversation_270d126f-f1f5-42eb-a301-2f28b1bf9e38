{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98721fd61fe5cbd72d9a06712a33c74830", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6368141b7f3290ee3223dcd0fdd351e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98510fde5d932027da3ea26db04c9306e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f6b9ddc11c5d3c15c8bf4e25bd788f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98510fde5d932027da3ea26db04c9306e4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ba05029996d48b2df6b68cafebdcc4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eed449ffee2c22e954b21a6023547aa1", "guid": "bfdfe7dc352907fc980b868725387e985a4701bfb396ff7fe563b9574f825f1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a095e52730e2d25f82dec96be3d871a", "guid": "bfdfe7dc352907fc980b868725387e98a26592acc145735c2394c90159dd9529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817bd3866530e48f6c25742d7ea186b9a", "guid": "bfdfe7dc352907fc980b868725387e98f8e186e4bab4796133b634ca26c55063", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0bd20a09e7b15b38ec86bc274d8e74", "guid": "bfdfe7dc352907fc980b868725387e98044f60d8ea10f913ba97c6d86da9df19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885fe93803d44dde5aad7b45114e207e6", "guid": "bfdfe7dc352907fc980b868725387e989b6ad60e7b95c1716e188736fe1eee33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98518fce15ded6e9d3873acf4083fd34b3", "guid": "bfdfe7dc352907fc980b868725387e984fe5dc36fe719fa442ac1197b5d383f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5d2f2ab67d20326b6fddc82cc6cce4", "guid": "bfdfe7dc352907fc980b868725387e98d4dfb204e18212beaed741524fcbb55e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9816eece8fa2ba59f770f630f956929a04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6e421ade252efb64e520f7f5872f656", "guid": "bfdfe7dc352907fc980b868725387e989ca4f975069d61649d6ae95352c21659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983654191ed9478fabbbe532923175b0f8", "guid": "bfdfe7dc352907fc980b868725387e9897fe82c5ba9691dd41a80efc72e14646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395a5482ad833c41dd22210367132e74", "guid": "bfdfe7dc352907fc980b868725387e9809a817319c0f73354421e723cfa60da0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5e5e0af17d077bdf8d912db7f288ac", "guid": "bfdfe7dc352907fc980b868725387e9880f33313c757ac94de161bd799cfcfd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e48565177c721e6ffcfe1443d7b10f", "guid": "bfdfe7dc352907fc980b868725387e984d15d254f259e2428a3e36b48420ab35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac3d80cb80459652f88126a7ac3f9cc4", "guid": "bfdfe7dc352907fc980b868725387e98e7c0bdb0d561183f531e4dca1e893889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98949c832f5c84d815214e42c46c165196", "guid": "bfdfe7dc352907fc980b868725387e982cedeb41dcb6eb21eac330a0cfa02dfa"}], "guid": "bfdfe7dc352907fc980b868725387e987632c234dce2d64abdc408ea70c15e76", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e984b3dd6f6bf4c0779e1d60634a99c2f95"}], "guid": "bfdfe7dc352907fc980b868725387e982d6a120ae5f4a6facee0bba78350796b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980289b8f5861c15d9c3a7bd2368cc9877", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core"}], "guid": "bfdfe7dc352907fc980b868725387e988e2765468126b8189d0a656452a5242d", "name": "flutter_sound", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a792d892ce1319f30820f36c4757210b", "name": "flutter_sound.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9899725a45848fd840c69f93a127d17625", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98322189594919984ff01c40f9b5e1787b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986bcd98ecf1b7fbd1595a509ee2c2094f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b0e62a133057a39810607d79eb0ecb5e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986bcd98ecf1b7fbd1595a509ee2c2094f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819b243b5141f74e259b8fb12026b9aaa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98984fbfec581d3739b85adad95bd4a500", "guid": "bfdfe7dc352907fc980b868725387e986ae60be9587789615b4e10e50475bb08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988773409f787a561990999a5010a384c1", "guid": "bfdfe7dc352907fc980b868725387e985973b6eebac388b540824916c2247d8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878f8d2b42a9a515c1191fffa7eae6bc6", "guid": "bfdfe7dc352907fc980b868725387e986d9663f1bc2ef162e336345829fa9e18", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ce216e27bfc5c82d94c3b6dbdd0b47c9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9830f4411320553f5fbd21eca10d2132c9", "guid": "bfdfe7dc352907fc980b868725387e988b3f4266a1f930b981a55ba525bc5e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133459991a4f491f62030b2fff0cce1c", "guid": "bfdfe7dc352907fc980b868725387e98cd70371b5dcb2bfbbfb1590f07cc7e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca2ebfbb6f79dc95110358fd97932af8", "guid": "bfdfe7dc352907fc980b868725387e9824dda72a0e16898b2c8422eae6ab8f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc4cf7dbfb99d8451f13c61e559d3255", "guid": "bfdfe7dc352907fc980b868725387e98fb72e318bab0ec556234a70a868ff5cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9c2b234209e5673ecec788f3d03735", "guid": "bfdfe7dc352907fc980b868725387e980b1262fac54d894850a9024e15baa06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980117a5bb0783bcb63e817326ada391fa", "guid": "bfdfe7dc352907fc980b868725387e98f87bb93c2e99680d8c1947df7852ef15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98094088be5a625a3c437f15a4480cb091", "guid": "bfdfe7dc352907fc980b868725387e9861a913b7cff0c50830996e1facfb879c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989218e3bd1ab3aacc5d5f41255c49bf4b", "guid": "bfdfe7dc352907fc980b868725387e987dc67768f4c1190e7bfb7f6c89cc455a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7890c496e27902827b20c3d41cb3466", "guid": "bfdfe7dc352907fc980b868725387e98d50dc0af8315a0dbab678707755df64a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818903293e21d21c716eba17ab7f99a09", "guid": "bfdfe7dc352907fc980b868725387e98f14dc7bef6097f1e371a61d4239d9dc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981692dceb7907ccd9b0ced317303b786b", "guid": "bfdfe7dc352907fc980b868725387e98a2704abb2a38bcc7f84344644a62d5a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98903a51df918c07797d472806159bbcf8", "guid": "bfdfe7dc352907fc980b868725387e98b6759613af963ccd0b22588072c30416"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648631df9b075ec3699882d57f6c3840", "guid": "bfdfe7dc352907fc980b868725387e986ece7fd4ff33fe2b2e5ebfbd41ab8bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca2ba429aaadde6d66d4515bced93ea", "guid": "bfdfe7dc352907fc980b868725387e9881148adec3bfe65ca7f93ae7849ec3f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a4d891d0b269ee2f8603d62ee7b53b2", "guid": "bfdfe7dc352907fc980b868725387e989e0d13f74b5cda81e6437ab6f8b1ec80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c9fa300d41417a8bd1603235052b99", "guid": "bfdfe7dc352907fc980b868725387e9875a1c7c3c2c11201c428b94a3b05284a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acc16ab082af0ee99bb5e1cece4db2a", "guid": "bfdfe7dc352907fc980b868725387e98664a161c6d36f9c8c2d5f2ef5d240766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980df713a5fc86250cc7868fba42e7e7a3", "guid": "bfdfe7dc352907fc980b868725387e987fde3927ae83619ea55d127234b360e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebb997effa6cad09e0dc0d4ee9b7b7b", "guid": "bfdfe7dc352907fc980b868725387e98e1e79651eeabdf35ee69311789747968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98773f82c262ab63dee7dee55e87a9a15d", "guid": "bfdfe7dc352907fc980b868725387e980b2acd0675569995dfb1018ec83f10cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980019b4f13df2cae877616517fa8860f7", "guid": "bfdfe7dc352907fc980b868725387e9813db13df649ef1f08a011ac7c1eb1bf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98620f2348d154f8336fffbe6a2edd19ed", "guid": "bfdfe7dc352907fc980b868725387e983d98897586845f3b6ffb1f20df656bfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5826703afac226b0fceb89041f6392b", "guid": "bfdfe7dc352907fc980b868725387e98fbd9ec4d83c04db8d212c0e6fdf41776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc893cf5c4047761e722a9a1876aa92", "guid": "bfdfe7dc352907fc980b868725387e98337ce706d896526296558ebcadddb218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806197b0c0a73325e0ab1276ea161ef1d", "guid": "bfdfe7dc352907fc980b868725387e9892aa5dc99dab71201ca97d8f836f48f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816b1e953bf80f270e9633eeb4c281fd1", "guid": "bfdfe7dc352907fc980b868725387e9824ff7bf869e0cafb85107c2362cc9137"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2d10d67620111d0eb28eda45a45a321", "guid": "bfdfe7dc352907fc980b868725387e98254bc73dd0520b325a99a8041bc28838"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2aae26a6982ebf73c5e9ea1669126b2", "guid": "bfdfe7dc352907fc980b868725387e987021f0cccf3c5fe1b37dc9438c841bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852dbf9bb10fd1cd1421d15a40c930aa0", "guid": "bfdfe7dc352907fc980b868725387e987f2c96b3ae128a235a325bc8a7629552"}], "guid": "bfdfe7dc352907fc980b868725387e98410e0633f4e5a8e540b1b829e47a98fe", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e989a5d712d50732a08381285579308740e"}], "guid": "bfdfe7dc352907fc980b868725387e982e45c5cda0996b8911d6e9b4cc3fc3de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9849389b8638bc690caeb3cbe25a9f6a20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
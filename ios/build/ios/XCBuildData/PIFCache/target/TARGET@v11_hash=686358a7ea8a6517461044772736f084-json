{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b621c92084b56f58be71490e5e3f37cf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987dbbc8b8af42b6b1cfb919b31ce6a498", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808fd85d0a263a85c3ebcd3f4ad5da103", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e761761b5a01b0b055fff1ea6013ebab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808fd85d0a263a85c3ebcd3f4ad5da103", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3b7f38d0b2b7c540d5341be5b3df1d3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6d1518d02c0d29090776c768cbb0aac", "guid": "bfdfe7dc352907fc980b868725387e981cddc28d89bee976bdadee16b8e9b78f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877234a64ea112c7d8457212e68850f2a", "guid": "bfdfe7dc352907fc980b868725387e98800ffdccee11dc7dd14b21323e7eb0b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229d387950ca0b2acc76523f357692a1", "guid": "bfdfe7dc352907fc980b868725387e98956f5254946917a12b8e227848754e48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1bfe25eb92d477dfc33707a3ad8445f", "guid": "bfdfe7dc352907fc980b868725387e989304f271da881bcfdf377ac4ea1e2b3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f89ec7ff8a614c2cb345dfda99a7d3", "guid": "bfdfe7dc352907fc980b868725387e9879de5ec5c14904c6ffbd30d8f12b7b09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df276c2d03a34aac16ba03b09a0c65d", "guid": "bfdfe7dc352907fc980b868725387e98b8cb3895a0af699fb4b60b5e604e52bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b1f6bb7fa8155c3bdd5188f31c435af", "guid": "bfdfe7dc352907fc980b868725387e98ec789162b9d68cbe22e62642848614cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893339b6e92512c69e882d19fa92f2c24", "guid": "bfdfe7dc352907fc980b868725387e98b09c1557c6c6e03150036c6a13375707"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c9f2c3fafcbde90ac6ab58389ad2098", "guid": "bfdfe7dc352907fc980b868725387e98e2a8c5598f98716128b2964d04c22b40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9338485a90ea5f0f460f048fc77d5b", "guid": "bfdfe7dc352907fc980b868725387e9807f08c3b6f07e547ef44b40d7ff4b4b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bcea8620caf7acee2c5df489deb1ec0", "guid": "bfdfe7dc352907fc980b868725387e9868414d1a76daa4454285cca28af6a901", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e417116c97b00b592164ca4374c909", "guid": "bfdfe7dc352907fc980b868725387e987de53750e4fdd2598c6f52a0378299d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890e677d83714c08e1454d8c06d0fb732", "guid": "bfdfe7dc352907fc980b868725387e98a59b0478000afaf1286caf3d7c083fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e12d970cdae71e1bb4d648de00cdc16", "guid": "bfdfe7dc352907fc980b868725387e98234e24bc767678d0d74424352a9f2172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48a697c013ff153da1914fc475bdc01", "guid": "bfdfe7dc352907fc980b868725387e9821108193ea5b04b5f2940fa37a5d1484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c077cf07170924f023a25f4e5e600bfc", "guid": "bfdfe7dc352907fc980b868725387e9876f610bd409cfd4048b760ce9ec28bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c36006b968626f31d0b9df112fb08a3", "guid": "bfdfe7dc352907fc980b868725387e98a602d3f14395eb11327708de1896552d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a64e9fb895ba023083048697aa046fb5", "guid": "bfdfe7dc352907fc980b868725387e98919988ac725b519c2d57e5e912dd398e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33469fbecda744bc15f60a463ffa92b", "guid": "bfdfe7dc352907fc980b868725387e982d69b70bd4aa4d08f1def9fc8a594162", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be54c821d93a9865d48c6172f228a490", "guid": "bfdfe7dc352907fc980b868725387e981f7943df1c31fadfab31bc02be607c2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1df26915271d973e8c315d44e77bfa", "guid": "bfdfe7dc352907fc980b868725387e98a5e77a1250217bf5960776f9b0dab7e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873366020dfd2d12ab019e0dfae5cb81f", "guid": "bfdfe7dc352907fc980b868725387e98522194ad6b1095d8633d8fa1ac4811eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e9b71ac590d63a1be1a869a2c55d1e", "guid": "bfdfe7dc352907fc980b868725387e981db5ffd25fd7ba4a71b414075eb85f28", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9829d0753dfe48a9ec1ecbaa9e8391c830", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f959cfc2015364dee27ba99328a23ecf", "guid": "bfdfe7dc352907fc980b868725387e9866e0fe844daab6a521c000af85fe05cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985414cb7521fdf21e0e59ab9dc97e1bc2", "guid": "bfdfe7dc352907fc980b868725387e98bf0127abca6fff1358de107ecab29da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec0ef4240392df8a872bf56cc3373dd", "guid": "bfdfe7dc352907fc980b868725387e9802f0d8199340c06b34e378de1bb96130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2660bfd26ae89402e241ed1152744e", "guid": "bfdfe7dc352907fc980b868725387e98e986f7171030e9d21d7dd77ca9c48333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a728bee32b408d960010da6c0e20450", "guid": "bfdfe7dc352907fc980b868725387e984838703ecd18dfcaa0851daf61dd1f9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849637992a8d34e3bb673b3df4fd9bc9c", "guid": "bfdfe7dc352907fc980b868725387e9888a7917dfa98bb0ffa67b781d3f4833f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871d65e9e593c8bffce8eaa21a77f42cb", "guid": "bfdfe7dc352907fc980b868725387e9804a43030f5075fb3385d233098137125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6fc43334aa1e0807ba6e59602e3fe9c", "guid": "bfdfe7dc352907fc980b868725387e986714e33f4be4f437e359e723725861ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981250895c300ee06902b36c31ed25fb40", "guid": "bfdfe7dc352907fc980b868725387e989929a9090170e9838ab487f63cce3760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b06fc8d5b8e48c92e3cae4e7e0510c5c", "guid": "bfdfe7dc352907fc980b868725387e9885eda5f4771528a061408820c5cef37a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862132214e13f6dd81f36c8be70f05204", "guid": "bfdfe7dc352907fc980b868725387e9821af5086e666414e5671ba5d01fe35a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e04ba603777af26b9bbcb44d4a3a8e", "guid": "bfdfe7dc352907fc980b868725387e987cece98524256bfa6fc5111d95a168b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d1dabace45010c1ef2d722e8310599e", "guid": "bfdfe7dc352907fc980b868725387e9871bf5b0be6e7de95890aba722dcd69e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef4b120298e8ed73f75abf7d4940584e", "guid": "bfdfe7dc352907fc980b868725387e9813274d4bce0848353e73398480b7047e"}], "guid": "bfdfe7dc352907fc980b868725387e989838e2d506c034b517de2921f7141605", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98a0ed43b0ecb5a384beabd55392746afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcbfcafc62f67bfc05776d7c770a69d", "guid": "bfdfe7dc352907fc980b868725387e98f183abf5388c104abe5976185aff018c"}], "guid": "bfdfe7dc352907fc980b868725387e9859a264596d77ec28948f708024125ddd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982bd32daf516d4ec4a4f4b02edb7aedd4", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e7e3fcc05c1b89ee150b2ff7f712c823", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
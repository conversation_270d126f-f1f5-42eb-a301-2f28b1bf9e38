{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b152341cdc435550fd72a2f1b540b200", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cc988ad47fa3fa84b8bd13094a23dd76", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d887b9fe91938e45821a29e86939faf", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98503dbd2aceacbe35694b4bf9e70cc0c9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d887b9fe91938e45821a29e86939faf", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c97282093e0a6a23ee0dd92bda4c3bf0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9821c2ef8289055df97354f0297fd1919b", "guid": "bfdfe7dc352907fc980b868725387e985c3a30acb02a8d4d38f38cbafa6d5e90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f6117f2b8c19159f6f6db1099c05a8", "guid": "bfdfe7dc352907fc980b868725387e980cd13fff08a7310d6e699c565fd02a71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdef8ec555f348fdf0c248b3a5236860", "guid": "bfdfe7dc352907fc980b868725387e980245167d0da1746e3f820fec9e3e5e03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872cf25c4f7d6a0c0b4afed452adb726e", "guid": "bfdfe7dc352907fc980b868725387e98f8e3df594077a47a0e3c7e3fd9bbe79c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de72c2a566ae5e4b49ae26e4b2b4d894", "guid": "bfdfe7dc352907fc980b868725387e9859fa2a3ddefa4ea28c79907213a1055b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98623ec2edd5ac1a39e4da2d32167679ed", "guid": "bfdfe7dc352907fc980b868725387e98220b1c8d8b966fefb5ae57945a38896b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c668088e3c140ed0576bb0a3f2a107c", "guid": "bfdfe7dc352907fc980b868725387e982d9eedaa9267a1bd24d8a5713a86b394", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d755f0ae110f5e19088c8219255a624", "guid": "bfdfe7dc352907fc980b868725387e983dbfe9b08a9b93fab36abf85167864a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee0b32307071a5582554cf8c45622af", "guid": "bfdfe7dc352907fc980b868725387e982a8d57a72c2ac54e08eb76070868c973", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40a7427b65f79928652e04d3071bcfc", "guid": "bfdfe7dc352907fc980b868725387e989e270d3c504303ef765cfcac335aec4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a7c0858e5ab414feeda80d11bc8b85", "guid": "bfdfe7dc352907fc980b868725387e98744f96af6c09416ccd58086cab1152b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca85f663bf691de3b4d89b541ffcd18d", "guid": "bfdfe7dc352907fc980b868725387e980e41fbfa9e05ce574fbb30bc06d31533", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103d3eb938d9e3a99c5eb7e20201e81d", "guid": "bfdfe7dc352907fc980b868725387e985e83409af691d4aeaf943312bdc04f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b77ede98529334a009f438194586d5", "guid": "bfdfe7dc352907fc980b868725387e982c309960768e3c3fcba27216921a4044", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caec7f9e25001ac0ff4897cc9eed5e70", "guid": "bfdfe7dc352907fc980b868725387e98f4d8da8cb0add03c4dd217892cf93c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08cd703e7242e5f2a1aeaa01b4ca73a", "guid": "bfdfe7dc352907fc980b868725387e981154ba2b5268862a45a6154259c2e060", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985015f54fed4b6b9b0167897bc49b0017", "guid": "bfdfe7dc352907fc980b868725387e98caf01b3ffd300826c7732375c63f93cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79d5abf11a4b6725b1304df57e02a12", "guid": "bfdfe7dc352907fc980b868725387e985a5865b9a6db27ed2a8d343f3961f27a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987478290518d1ad25a45bd46fd047f1b4", "guid": "bfdfe7dc352907fc980b868725387e98780c08aa35f77319becc422a8c998d42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ead882ae75dfd09f17cf71b08f27cf4", "guid": "bfdfe7dc352907fc980b868725387e988f03e92b2a6fe3e31ab1b05ca9739fda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce9a863ce91af039a63f753d6d13ecd", "guid": "bfdfe7dc352907fc980b868725387e98a9e13739df32666a5ee43e8e894b9a46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831b3bfd9fa6bbb9624e7d2123316ed74", "guid": "bfdfe7dc352907fc980b868725387e98cc4f64f131be6a4b519d5cc196575494", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd68d20d74645f61bb70b34566b753b2", "guid": "bfdfe7dc352907fc980b868725387e98aaad9ea93f7782b1c6376fb1bb3e4b9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64634583e7a08d8df156ac0fa3ea455", "guid": "bfdfe7dc352907fc980b868725387e98d4e5caef436e5dc26e7013b29a118cca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc37af580f19bd751b16dd57b0f65d41", "guid": "bfdfe7dc352907fc980b868725387e98253f11fc834339132382fa7baf72bf3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a906ecfe58c1d9522bee17c3bb69cd", "guid": "bfdfe7dc352907fc980b868725387e982d80d8b21c840f4d4de8dccb7688764f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3808e8957287a138199ade74eacbdb0", "guid": "bfdfe7dc352907fc980b868725387e983cc51db14f9bd5880406e80097cef09f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818cb9e502c360c3f63da2cffd978ac24", "guid": "bfdfe7dc352907fc980b868725387e98dd4c7b5e421fb1dc482400716bd0b7a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981019473e409f194626b4d791cd8d2b43", "guid": "bfdfe7dc352907fc980b868725387e98889ddfb748565a0b0788f61d4c74ec29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ee83728e3d0d4f52a80766e516140c", "guid": "bfdfe7dc352907fc980b868725387e98592d13e37efb3487ff6c324ed41aa2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8d3cc2e161307ef60495888645cc337", "guid": "bfdfe7dc352907fc980b868725387e980d6b36566b42d73f8b1fb9aca2e5f3b9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a3671c41804445387ceec941a0db5bb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d965688c93ef2fe791daa4d2a50a594f", "guid": "bfdfe7dc352907fc980b868725387e981598b6994763dbc8dee14f1bb9622274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c1d3bafea7a2d6be63f38c81aff7b7", "guid": "bfdfe7dc352907fc980b868725387e98a45f4218f5d399117914bd652d832b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b5d11777a14cda77de2196ce13120e", "guid": "bfdfe7dc352907fc980b868725387e984a277ea16eb4f355e1b40a4c903eec79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc49c57318bdd603f902b527b3407e91", "guid": "bfdfe7dc352907fc980b868725387e987a35dc2f7bce3fe39c063f2a0acadc9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca5e0758153c91806daa7fbe41d86fd", "guid": "bfdfe7dc352907fc980b868725387e98064988daa825f6d9e2bf6e493516ed54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9dfe02d9423e63de8f5c869a8f1c9e9", "guid": "bfdfe7dc352907fc980b868725387e9864de79651ce3f47a00936d4a0ff08640"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae27c5b077971e46fb0dc0501c5b95d", "guid": "bfdfe7dc352907fc980b868725387e98edba404a0f8936a28aedfa68cf53aa4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6bdee54211940143ea428c3ff8b90aa", "guid": "bfdfe7dc352907fc980b868725387e98f0d3bf3bbccf017c8af12ea79959c7b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982edae11e7a20c20597021f5a7ec14ec3", "guid": "bfdfe7dc352907fc980b868725387e98f09ff285e4b46110bfb3dd0ab90d8e83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bde59938cbf756e326b85380c7cc152", "guid": "bfdfe7dc352907fc980b868725387e9800e750b9da109781e37c58f9d9be25b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc640776f76f50fa005543eaeead1b7", "guid": "bfdfe7dc352907fc980b868725387e98fc94b1a2ab7346970d52b419d0a6b714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822fef358a39b0fb0c19fdd857b579539", "guid": "bfdfe7dc352907fc980b868725387e98b3c82ac4f7925475a104faee66e9a26e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cacd33444da956e3a8c88f327103e569", "guid": "bfdfe7dc352907fc980b868725387e98f2cf59d07c29b4979e9598bbf1549cbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc9cafc2fe45750bcd0f492fdc055e63", "guid": "bfdfe7dc352907fc980b868725387e98146b612e6acee88b411d9113ae2c867d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae1de0e4a42bc68e6eda2ed6e532c8fc", "guid": "bfdfe7dc352907fc980b868725387e98b0b3051c556276d9f45c53686841f680"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0492e7dc60c691cf6e7f199ae00b83", "guid": "bfdfe7dc352907fc980b868725387e984ecf8073b0a83f9cec16b8de003f13aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c149604a5a92cd232c9531d3705c47", "guid": "bfdfe7dc352907fc980b868725387e98cf3e521499a8ff590bcfe7d6970aa0d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48fd9fc3c550844a650177f0b7b5ec7", "guid": "bfdfe7dc352907fc980b868725387e986f4e5b62cf8b2ec751f90e8cb2ee9e41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298abf4607b38200dbbb9beb8b476cd9", "guid": "bfdfe7dc352907fc980b868725387e9882145e910cdafcd339d6a98ab8fb7ebf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb7b6f59fbee325b51213017fbfa9326", "guid": "bfdfe7dc352907fc980b868725387e983aae7973a6eba71658139568a2f2965c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980892f1c70bf7f3f947fffeff3d3e8108", "guid": "bfdfe7dc352907fc980b868725387e9827c249b98f5a95b3c33edc080e4d09cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a955909a0193439d8a77ecac96998e4b", "guid": "bfdfe7dc352907fc980b868725387e986bea84b36c264d7adee0a7aad956139e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3291579449b90a6b55b9bfe6a88f988", "guid": "bfdfe7dc352907fc980b868725387e9860e88162f386a19d1b0351ac407eaef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98389379cd45dedb293aa918bfbae0db5e", "guid": "bfdfe7dc352907fc980b868725387e9802e37410d75ea4f1f7f6bf8081f83d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f786990385ffb24aec09b987faa3b96", "guid": "bfdfe7dc352907fc980b868725387e987712359052872746b2ebc3e501354c80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e796d617799696507e58c41b07eeaac", "guid": "bfdfe7dc352907fc980b868725387e9888c4b407d046c77866bbbdfe6b649e6d"}], "guid": "bfdfe7dc352907fc980b868725387e98ae5fb5115ee0f9bf85310fe548d2e1f5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98f08a215adc829a9d5b26cd84f4575dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1ace440e065e19031c3ac9f287ad59", "guid": "bfdfe7dc352907fc980b868725387e988b71db27d3486a0d362a174567ddbbdb"}], "guid": "bfdfe7dc352907fc980b868725387e983422294bb4477d3d6ca5a69a648a36ef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a1783e9516c27edf32acb70f38864ea7", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e983c621b703cdde18b9ad26f4bd46362f8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef53d12fa922d3c18d1ed4a3a68a632a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98976b17c966ba5dc27a7eeb2980286c51", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e21217fb8336ba2ac8b72875e439194b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9886aa2435034ee04733064d2ae00fcd3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e21217fb8336ba2ac8b72875e439194b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a06c86f5cdf28196fc49e7a5ce40ce13", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982ba5aba5add69d28651ebb1f7d7b5dc4", "guid": "bfdfe7dc352907fc980b868725387e98cc8d28ab3e46a6c6b3388a7621055d28", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989137ae5736ba8b8492df6b484fcf2d74", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880e02e804dee43d35972ad52a1c17e0f", "guid": "bfdfe7dc352907fc980b868725387e98aa69ecc625ba0a2431a15fde3ed825c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c133b31fad9878bf5d0cfc79b1bc078", "guid": "bfdfe7dc352907fc980b868725387e98aff831954aecc66aba7d150ec7fe03ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983581e674b31f5cec1f6a0f1c83eae3b4", "guid": "bfdfe7dc352907fc980b868725387e98faea7437b854c9ebf7822c0f48810b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c6eec959bde9c3be09c0ef1113ffefb", "guid": "bfdfe7dc352907fc980b868725387e989c333425f20a52251466f40cd78ab779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fdb5c4bf2ce9844346c5933a9780da3", "guid": "bfdfe7dc352907fc980b868725387e98206bdc839666c53604c1318cec6efebb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847224c5cff807734ee6442dbfe823fcf", "guid": "bfdfe7dc352907fc980b868725387e98d7b5da9fce1d531742c33ff3ae4f4aea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d2a5131be58b8b9d732ea372e73f95a", "guid": "bfdfe7dc352907fc980b868725387e984addfe9083f21cdceff96c1e3fefcc3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0344d6bd6dffbfa95c52f1bd05f27dc", "guid": "bfdfe7dc352907fc980b868725387e98ac8676f22ea5391345c32b5d3139c936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ac9d2bc0ddf58b135dab44fdb2babc", "guid": "bfdfe7dc352907fc980b868725387e984ef3a857ecf58ef58d2c7ea3ef668901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e0a6d0f19dffdbf58758597a9e2ad0", "guid": "bfdfe7dc352907fc980b868725387e98a686d9e4e5041334b556742c9b795e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98665574359049f8d34778f130eaa3d100", "guid": "bfdfe7dc352907fc980b868725387e9846f0e7fd990dde942c0a46b3ce95292f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98142ec92749970777843553b3d4524005", "guid": "bfdfe7dc352907fc980b868725387e9885ba9e26266b18d43c296e9abf60e9b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f980b68e2e5394b9c6f76a57be7c6972", "guid": "bfdfe7dc352907fc980b868725387e98166d1094b470f140f5a45c70d652163e"}], "guid": "bfdfe7dc352907fc980b868725387e98c8c823b3e62865c5a78af7450b44c0a7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98bee3e06cb13768855b1cc87e557f4133"}], "guid": "bfdfe7dc352907fc980b868725387e985562c60dc460d7d8b9e17501ae12d240", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98df02241f397fbed7302eb8850602fad4", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e987f320178d99cd53cd00dfd688e4ed472", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
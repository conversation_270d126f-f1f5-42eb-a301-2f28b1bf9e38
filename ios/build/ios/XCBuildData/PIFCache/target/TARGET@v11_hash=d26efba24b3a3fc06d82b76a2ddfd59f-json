{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a90ebaccbea2931c8c05987f1a05e29", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ea7a656699e95c055592003825af5fef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2c4633a3567ca1c1250bd021d9cbb04", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989366fe47b4e34a11a6fda22406f465cd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2c4633a3567ca1c1250bd021d9cbb04", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a1347525dfcd2cd6c3f759b3c164440", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6f0a548ed600859002e25969d54e5c0", "guid": "bfdfe7dc352907fc980b868725387e9865e73b9d0442125aa3e308b427335f16", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e9d2ff175d7901dd4eb0dd628cbe16e0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa2b0350db41f84a1d9b6954a07a9d8c", "guid": "bfdfe7dc352907fc980b868725387e988c5e1ebd013038f8c00295a7eff03ff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33907fe985381a7a1de700622436583", "guid": "bfdfe7dc352907fc980b868725387e986f7ef7ae63908c0ba3ce0a126039392d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7348271831a8e936bca8106f4efc6d1", "guid": "bfdfe7dc352907fc980b868725387e98968ab88fbd8ce9a528328efb3904a5c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806f2f7736e12db8c087c2f9a7a3314c1", "guid": "bfdfe7dc352907fc980b868725387e986eeb36894bf01cb06b264dddd823d0fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edb63585feb0030bd0bb5659717456f8", "guid": "bfdfe7dc352907fc980b868725387e9819ac7214fd4fa21bd39250290f8825a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a4a8b02eb354d1d496edaf1d1e23cc4", "guid": "bfdfe7dc352907fc980b868725387e9860b644a031190bee2562292f22f96e28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979b773ad8316f8540e97535d05ee004", "guid": "bfdfe7dc352907fc980b868725387e989cafcf12b29801cc8e024fac99e7aa8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c7ffd2b5c392b28da3ab77bd713598", "guid": "bfdfe7dc352907fc980b868725387e9803f0463ab9f3fe0c6177e45eb3753a9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821825a866df530d6ef6ad6f73dacc404", "guid": "bfdfe7dc352907fc980b868725387e984af971572127125d9797b59660d06e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f43dc121f4d3560131a932bd7930b9a", "guid": "bfdfe7dc352907fc980b868725387e989d840a06659c90d87f7a1777b113b6ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837376218c577b37d931031f836869d4a", "guid": "bfdfe7dc352907fc980b868725387e9891f555b4120b4b59f9ad4f4f256b38a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886ab858e53f818114f5edf9218773f27", "guid": "bfdfe7dc352907fc980b868725387e9805e1eed71bf1ad3bc46215fc68bdf84f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75d5b09bc41e61e68eac1e5b2315f7a", "guid": "bfdfe7dc352907fc980b868725387e98a4937d86a5cc5790a35a344a682d046d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a200f977f10ef2f570f018fc02ef1b5", "guid": "bfdfe7dc352907fc980b868725387e98f82d76c556523f350a6739b8aedc1fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1c63495df3bffb7782c55c0232567e", "guid": "bfdfe7dc352907fc980b868725387e98af43f104871c10ab944ca7bc3af56558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3246e99effc618a9e65776c8e3204d2", "guid": "bfdfe7dc352907fc980b868725387e98cc9b4de2aed5ead1591ce1f8e9be90c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49035504cf6f3e3ae693af5b71a6257", "guid": "bfdfe7dc352907fc980b868725387e982e51f108c453faae84d92141dce0f345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd35b969af8a16d943a9fda1562be5a1", "guid": "bfdfe7dc352907fc980b868725387e98a96828421ab15f8ead93b89617c119ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98921260b01e5801d0f8a5977e817a24c7", "guid": "bfdfe7dc352907fc980b868725387e98447f0b854cf25d429cd792764f3d5ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d0a3a75d11a86aff152cb5042db2a1", "guid": "bfdfe7dc352907fc980b868725387e981731a836516e8874011aec128ae93ee2"}], "guid": "bfdfe7dc352907fc980b868725387e982b0105d2f19134d7c66d63784d4a5df2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98db6878f812527ba6a2d20db769f75d90"}], "guid": "bfdfe7dc352907fc980b868725387e98661b1fc21c3fce5b7f6e1d8a14a8b70d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9869819b73cf3ffe329ca0c7761efc24b8", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e98cc8ad42a51555cc1b7a47f37abe2cec6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98652875102f849012ac86d460222397f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff4bf57d95a769fe87dce52859753019", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985df5e5919232d3b1798e0aa62d605b49", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98659793ec56f3ea1bf9066aad0b6145e0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985df5e5919232d3b1798e0aa62d605b49", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5ead90fd5e1e2778425c851f9adb871", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c930278c72b41e125e71c0a7381f2c52", "guid": "bfdfe7dc352907fc980b868725387e98cc909c2dc3bef0c0a27eaad549cbd2d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d666f53192e3d49226258ea2726d660", "guid": "bfdfe7dc352907fc980b868725387e98d016385dcaf1c16176aaca71186fc4fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ed1b679289b1790815985d8becf05b", "guid": "bfdfe7dc352907fc980b868725387e98101e626dcef8f8073b688f079920c3e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b272bb283761e3cbfafa0e3f3daea6e0", "guid": "bfdfe7dc352907fc980b868725387e98c7eac176dfac275fac4aba7e5c8f1a6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b48095f5a37e4a9e17c5e6c8c77e478d", "guid": "bfdfe7dc352907fc980b868725387e985e0ae4a2533ec4164d2ac42fb98662df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632862377e82be818bbbd15decf0e095", "guid": "bfdfe7dc352907fc980b868725387e98be8dcc46f84ad58a0c1ae134a4f90645", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df927230e57ef792ac635a465525db8a", "guid": "bfdfe7dc352907fc980b868725387e9864df496549c7e45ff9f0a17d45beb47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dfec1249d9294e1dc487ac0dabc0b74", "guid": "bfdfe7dc352907fc980b868725387e981b01444e710ad85607ef3c034e56811f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813d7a18647141d053fe262a0035a7b8", "guid": "bfdfe7dc352907fc980b868725387e982df34d65a1d37c3a26b259bb5a120e22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28a6d8d6935366559dc3ca0cdfd9a08", "guid": "bfdfe7dc352907fc980b868725387e98332cd7d817e31da9294117740da26e63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830e531b8fc37a904a7c28020d3758062", "guid": "bfdfe7dc352907fc980b868725387e98b65cb43d6e743b5ae1876f98400a32b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675431a241672449e307cdebf3c28633", "guid": "bfdfe7dc352907fc980b868725387e982b001ec7cdace6d5d94d5878e003aa74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4b6b311efbe336c5195a84abc6766a", "guid": "bfdfe7dc352907fc980b868725387e98c740ff87b4173570575c534748c97441", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982953c2c08c844c59d163e08aabac8f28", "guid": "bfdfe7dc352907fc980b868725387e9893d4c496e0eb8acc07f20783bd617c77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08b97c60415ce7d3a0cf80f62a60475", "guid": "bfdfe7dc352907fc980b868725387e98c910b11a3ab6f88204ff886e9d00e183"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b12473a3c8d4ad4607cf61ecf959c3", "guid": "bfdfe7dc352907fc980b868725387e985e848dd2052511ab6b4e50d0f4c3fbab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986491f8ac2fec5d21e888dcb759d4e4d5", "guid": "bfdfe7dc352907fc980b868725387e982bac632ce68e39a96904cf4d09934a12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c5aa17c2cc84d05c358bc1210fa13f7", "guid": "bfdfe7dc352907fc980b868725387e989e632365ca341ff609400638cf219d95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827dc90dfede4b8478bcef2854e6605ab", "guid": "bfdfe7dc352907fc980b868725387e986333edd0ed5b2cb0856dd56a724e8626", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed0f5066979dd3f2fe4eda17ed64b3b", "guid": "bfdfe7dc352907fc980b868725387e98d3f29b4cffa091edab5dce9c68719b73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988860a2f3e27da55b112c081d5b4e9ad6", "guid": "bfdfe7dc352907fc980b868725387e9894fc7b5aab138a863ea39f9b0651e15e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd77fe22a800ea9f7441dea54b9d6b6", "guid": "bfdfe7dc352907fc980b868725387e98a3360b1031e50fd7966ba40d7a89e049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4bae0f3f4aae9d740f2e480b324c826", "guid": "bfdfe7dc352907fc980b868725387e9819897445c8157c138f75f1c0d70b5ba8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd3915781beeadc7a1e68640f7504b9", "guid": "bfdfe7dc352907fc980b868725387e985c8732e7702d704af3e6e4c11669a868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817785ea8937fcb90d408809e0555094b", "guid": "bfdfe7dc352907fc980b868725387e983dc5849ba1cc540742f3fd715afee35a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e05c9713d2aa3a426118b4693513b23", "guid": "bfdfe7dc352907fc980b868725387e980c2a33f5466eb4a96e94c567831b9fbd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df9f2e2bc941aaa7da04387bb63e3912", "guid": "bfdfe7dc352907fc980b868725387e9834f3d251afecb81998e2745517bd2f64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226a7082612c2665b911a553b50c02d5", "guid": "bfdfe7dc352907fc980b868725387e98f144ac8142e8f81cce7240f5fede41aa"}], "guid": "bfdfe7dc352907fc980b868725387e98b590ee1e9db222672141411b19c41476", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98514f604af8179fab1d0a1e6626e8438e", "guid": "bfdfe7dc352907fc980b868725387e98c942dfa70ef7b8e11d24ce2b68abe122"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc37692b524e19bec0fe2a72c840038", "guid": "bfdfe7dc352907fc980b868725387e98eb0784d8ecf6f0bce1f4be67e4c7ad4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a3629a5a3e8e9b134862738fb22dcc2", "guid": "bfdfe7dc352907fc980b868725387e985222fd54e654f5c74d9bb9019d041a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd388473ce5ec08148c2cd1f83ff49c0", "guid": "bfdfe7dc352907fc980b868725387e98fe71a85c0030463261a98a9f3e83b0c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b5cc229e7cd15424c1897f4a22482f4", "guid": "bfdfe7dc352907fc980b868725387e982f9f464be3f7c5da2d9b0411e27c865f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ccba8e5350d8fe9f3484593d43f842", "guid": "bfdfe7dc352907fc980b868725387e98bc62e00b0e4ee90030195fc4bc25ae18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208da60e1f6c2d8e42654a2adb930d8a", "guid": "bfdfe7dc352907fc980b868725387e98ab1e0b9f52fd8f9d67f1f8b953b07ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a3ccffa198932b40946cea9712f0aff", "guid": "bfdfe7dc352907fc980b868725387e9807b15ecb878efc6008aae6eaeeeb5749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98719514d814be763b3f91d1bee360a104", "guid": "bfdfe7dc352907fc980b868725387e98a875390fb80e0f2d767b8e472368c731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cccf24bf5533dec2d95de15a57b48a7", "guid": "bfdfe7dc352907fc980b868725387e988e0def4bd7828fb336cef9f5e93a3402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf2bb4a965f86179b65400f6cc003cef", "guid": "bfdfe7dc352907fc980b868725387e98ebcf8a7a1024bbd1cbabd65e91eb49a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee40dc918bf5b7ed6bb1d5d62e0ee7e5", "guid": "bfdfe7dc352907fc980b868725387e98328aed3fa39fe5ae19f23ed836ba9764"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983594d0c96dc2ea5d9a7d0f2af3f6689e", "guid": "bfdfe7dc352907fc980b868725387e986bc07bb2342c523af44dca6521e6419b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe67d22bc90e50010f4f06a3c7af5b1f", "guid": "bfdfe7dc352907fc980b868725387e98380e755a432193c2061b120a8b2603a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833791a1c45e520cb5041d2204538fcec", "guid": "bfdfe7dc352907fc980b868725387e984e035156c4f683b900a7ff974ae81774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879299b1da4e92cfc2500c335504e0582", "guid": "bfdfe7dc352907fc980b868725387e98d8c3154892497d98998a1757af101a31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa7ab0cdca6994d12e41e45866aa9dc5", "guid": "bfdfe7dc352907fc980b868725387e98e21ca7fdabea5a66acaabd6a30286ec0"}], "guid": "bfdfe7dc352907fc980b868725387e98eb75bb97f0d3a12f857d9816f10eb5a8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98ca5c8ebfdfe2ca23c12c6749d5404a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f445a9bdfb8126a0334a1f4c6d511e91", "guid": "bfdfe7dc352907fc980b868725387e98603c72bc4830fc955195804e5e1d132d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef754bd96bb155059b87394e54a34734", "guid": "bfdfe7dc352907fc980b868725387e98cde4b6f62964724265644956deb2ee59"}], "guid": "bfdfe7dc352907fc980b868725387e98435c49f6b26e3b39ba467a0594aacaad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acf0fc2d35b11e14ee7b220c19b32776", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e983c5d5412c1e63c38812596b773e28e5d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f7ab4811a991a8d84ed1299d31c12ef", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a507aefa2bcb928c4e446ef3b928722", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988736a77b3e99724a81b23742a2ee5bff", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a70cc1357bf6c994b9489f37be19c91", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988736a77b3e99724a81b23742a2ee5bff", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Documents/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_SPEECH_RECOGNIZER=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847459f47783032052024464440fb12bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a8852d9284a640ef0391ca5a827e8438", "guid": "bfdfe7dc352907fc980b868725387e9828926487bdb876b59a050dc07dbc8b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f667dfc6ab6300e6651650817f0dda06", "guid": "bfdfe7dc352907fc980b868725387e9866a0a0bbebb513f0618c2a6977070bf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b48610abe2615bff02964a9d1df2e14c", "guid": "bfdfe7dc352907fc980b868725387e983be8d32d527835e15448dd9f8157fdb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad80d0d49934f6f461027c47ca0e401", "guid": "bfdfe7dc352907fc980b868725387e9882dd6d21fa6369bcc8cfb6d050dd8595", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e971a08783a2ba1550ea82f6c06af80", "guid": "bfdfe7dc352907fc980b868725387e984632e7637ed45e2c5c711bc3be9285b1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98506f1b9923d6cc6ab9b200cc99b35917", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989a377b2cdd0b8a6c65d29dbfae5d1cf4", "guid": "bfdfe7dc352907fc980b868725387e9831058237beea73fc8f15a33c99ba924d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4dbf91cfe4d956d7eb75e16be32a6ce", "guid": "bfdfe7dc352907fc980b868725387e985a98e3e5eee6d8913554bec6243cbec5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b316b0e4e6dbf41850ada1c90beed6c", "guid": "bfdfe7dc352907fc980b868725387e98275f9be6ea3acbf18fb01c963bce234b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986775b6cfa8a58fdc16ad4def16590861", "guid": "bfdfe7dc352907fc980b868725387e981bac0a0649c577d194c64e78e3f5effb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862fdba53928298a423409c0b09a8dec1", "guid": "bfdfe7dc352907fc980b868725387e985300c21d247b0a695c9f8884ba9a7c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983398b8297ce0cb8552cd0a65cfaefe56", "guid": "bfdfe7dc352907fc980b868725387e9864aa11913ba635a9365f017e6fbc8d9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276d2c4a8190f4f4713a46d08f3ae99b", "guid": "bfdfe7dc352907fc980b868725387e98467fbebcfc805436d0c58e1d3f5a829d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff668be292c4358dccea6371cf5e92a", "guid": "bfdfe7dc352907fc980b868725387e980de8fc2d0f06d2a24de4fe3e046a2a12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e9ac7cc9e146da328adc20962d425a", "guid": "bfdfe7dc352907fc980b868725387e9823e0b5b5ffe483f665eeab4d552f0afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc29d40c72bc4629f2e94256c7dd2bf", "guid": "bfdfe7dc352907fc980b868725387e9899bd346eabaca2daa4c3c6540ca8132e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c3a092964dd96d2cb9053c16b66f1de", "guid": "bfdfe7dc352907fc980b868725387e987a06e86c00920faf576e5ae814029592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ff91154e5f58a2c73889f4d932b3e20", "guid": "bfdfe7dc352907fc980b868725387e985bbd5e364863b593244002fe9a48eb8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0be19652ee9a9238107f62a4f08579b", "guid": "bfdfe7dc352907fc980b868725387e9822d2702f3f9c8eb3fd754ad90e6b45a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ced6ddfa55c2d511302650600f45225b", "guid": "bfdfe7dc352907fc980b868725387e98bc2e662ded9b9a9b8864ec02b1732022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cde7fcf8f776b9e123c1bddb3966da6", "guid": "bfdfe7dc352907fc980b868725387e98b080dc504027fa2212afafa19f6ff50e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1133832cf1b00b960d13f48bd1bf58a", "guid": "bfdfe7dc352907fc980b868725387e9812053af5d6f106197e189af3a81125a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb7bb0d8cdb0b56d755178097499d6f2", "guid": "bfdfe7dc352907fc980b868725387e987e20d9056a8e399adbc13657f9f2ce57"}], "guid": "bfdfe7dc352907fc980b868725387e980799124e6b1bec10a31601734d965978", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d52101cdc11228d3effcdf629786ec", "guid": "bfdfe7dc352907fc980b868725387e98e50d8ce8b45885cf65405c3be67f6abe"}], "guid": "bfdfe7dc352907fc980b868725387e98f798410d711bf2060f608703a93a4cb2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d1f3bbcb4cc251b86cd820939e996acd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef586bf23362aebce5c719b4482fa695", "name": "media_kit_video", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9897cdf84f28cf59650494803c009f76dc", "name": "media_kit_video.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
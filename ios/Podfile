source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
# Uncomment this line to define a global platform for your project
platform :ios, '15.6'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'ShareExtension' do
    inherit! :search_paths
  end
  target 'ActionExtension' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    
    flutter_additional_ios_build_settings(target)

        # Start of the permission_handler configuration
        target.build_configurations.each do |config|
          config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.6'

          # You can enable the permissions needed here. For example to enable camera
          # permission, just remove the `#` character in front so it looks like this:
          #
          # ## dart: PermissionGroup.camera
          # 'PERMISSION_CAMERA=1'
          #
          #  Preprocessor definitions can be found at: https://github.com/Baseflow/flutter-permission-handler/blob/master/permission_handler_apple/ios/Classes/PermissionHandlerEnums.h
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
            '$(inherited)',
    
            ## dart: [PermissionGroup.calendarWriteOnly, PermissionGroup.calendar (iOS 16 and below)]
            # 'PERMISSION_EVENTS=1',
       
            ## dart: [PermissionGroup.calendarFullAccess, PermissionGroup.calendar (iOS 17 and above)]
            # 'PERMISSION_EVENTS_FULL_ACCESS=1',
      
            ## dart: PermissionGroup.reminders
            # 'PERMISSION_REMINDERS=1',
    
            ## dart: PermissionGroup.contacts
            # 'PERMISSION_CONTACTS=1',
    
            ## dart: PermissionGroup.camera
            # 'PERMISSION_CAMERA=1',
    
            ## dart: PermissionGroup.microphone
             'PERMISSION_MICROPHONE=1',
    
            ## dart: PermissionGroup.speech
            'PERMISSION_SPEECH_RECOGNIZER=1',
    
            ## dart: PermissionGroup.photos
            'PERMISSION_PHOTOS=1',
    
            ## dart: PermissionGroup.photosAddOnly
            # 'PERMISSION_PHOTOS_ADD_ONLY=1',
    
            ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
            # 'PERMISSION_LOCATION=1',
    
            ## dart: PermissionGroup.notification
            # 'PERMISSION_NOTIFICATIONS=1',
    
            ## dart: PermissionGroup.mediaLibrary
            # 'PERMISSION_MEDIA_LIBRARY=1',
    
            ## dart: PermissionGroup.sensors
            # 'PERMISSION_SENSORS=1',
    
            ## dart: PermissionGroup.bluetooth
            # 'PERMISSION_BLUETOOTH=1',
    
            ## dart: PermissionGroup.appTrackingTransparency
            # 'PERMISSION_APP_TRACKING_TRANSPARENCY=1',
    
            ## dart: PermissionGroup.criticalAlerts
            # 'PERMISSION_CRITICAL_ALERTS=1'
          ]
    
        end
     
  end
end
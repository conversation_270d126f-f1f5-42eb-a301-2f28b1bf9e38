import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

import java.util.regex.Matcher
import java.util.regex.Pattern

plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}
tasks.withType(KotlinCompile).configureEach {
    kotlinOptions.jvmTarget = "17"
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader -> localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

JavaVersion getJavaFromHome(String path) {
    def releaseFile = new File(path, "release")

    if (releaseFile.exists()) {
        def pattern = Pattern.compile("JAVA_VERSION=\"(\\d+)\"")
        def matcher = pattern.matcher(releaseFile.text)
        if (matcher.find()) {
            def version = matcher.group(1)
            logger.lifecycle("Get java version from release file: $version")
            return JavaVersion.toVersion(version)
        }
    } else {
        def javaBin = new File("${path}/bin/java")
        if (javaBin.exists()) {
            return JavaVersion.VERSION_1_8
        } else if (new File("${path}/bin/java.exe").exists()) {
            return JavaVersion.VERSION_1_8
        }

        return null
    }
}
JavaVersion getJavaVersion() {
    try {
        // 0. Get java.version property from project's gradle.properties
        // The version value is like 1.8, 11, 17, 21, etc.
        // Also see: https://docs.gradle.org/current/javadoc/org/gradle/api/JavaVersion.html#toVersion-java.lang.Object-
        JavaVersion res

        def javaVersion = project.rootProject.findProperty("java.version")
        if (javaVersion != null) {
            res = JavaVersion.toVersion(javaVersion)
            logger.lifecycle("Get java version from project's gradle.properties: $javaVersion")
            if (res != null) {
                return res
            }
        }

        String javaHome
        // 1. read from JAVA_HOME environment variable
        javaHome = System.getenv("JAVA_HOME")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from JAVA_HOME: $javaHome")
            if (res != null) {
                return res
            }
        }

        // 2. read gradle.properties
        javaHome = project.rootProject.findProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from gradle.properties: $javaHome")
            if (res != null) {
                return res
            }
        }

        // 3. read from property with java.home
        javaHome = System.getProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from java.home: $javaHome")
            if (res != null) {
                return res
            }
        }

        if (res != null) {
            return res
        }
    } catch (Exception e) {
        e.printStackTrace()
    }

    // last, use default version with current
    return JavaVersion.current()
}
android {
    namespace "com.seedtu.ls"
    compileSdkVersion 35
    ndkVersion "27.0.12077973"

    compileOptions {
        def version = getJavaVersion()
        sourceCompatibility = version
        targetCompatibility = version
    }

    signingConfigs {
        debug {
            keyAlias DEBUG_KEYALIAS
            keyPassword KEY_PASSWORD
            storeFile file(KEYSTORE_FILE)
            storePassword KEY_PASSWORD
        }
        release {
            keyAlias KEYALIAS
            keyPassword KEY_PASSWORD
            storeFile file(KEYSTORE_FILE)
            storePassword KEY_PASSWORD
        }
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        ndk {
            // 设置支持的 so 库架构 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            abiFilters 'arm64-v8a'
        }
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.seedtu.ls"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        manifestPlaceholders["aliyunpan-appid"] = "0077298293394e1fa771f0b0e005800f"
    }
    lintOptions {
        // 如打包出现Failed to transform libs.jar to match attributes
        checkReleaseBuilds false
        abortOnError false
    }
    lint {
        disable "InlinedApi", "ObsoleteSdkInt", "NewApi", "UnusedAttribute"
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }
    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
}

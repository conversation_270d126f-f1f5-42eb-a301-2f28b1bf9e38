import org.yaml.snakeyaml.Yaml
import java.util.regex.Matcher
import java.util.regex.Pattern
group = "com.github.sososdk.aliyunpan_flutter_sdk_auth"
version = "1.0-SNAPSHOT"

buildscript {
    ext.kotlin_version = "1.9.23"
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.yaml:snakeyaml:2.0"
        classpath("com.android.tools.build:gradle:8.7.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

JavaVersion getJavaFromHome(String path) {
    def releaseFile = new File(path, "release")

    if (releaseFile.exists()) {
        def pattern = Pattern.compile("JAVA_VERSION=\"(\\d+)\"")
        def matcher = pattern.matcher(releaseFile.text)
        if (matcher.find()) {
            def version = matcher.group(1)
            logger.lifecycle("Get java version from release file: $version")
            return JavaVersion.toVersion(version)
        }
    } else {
        def javaBin = new File("${path}/bin/java")
        if (javaBin.exists()) {
            return JavaVersion.VERSION_1_8
        } else if (new File("${path}/bin/java.exe").exists()) {
            return JavaVersion.VERSION_1_8
        }

        return null
    }
}
JavaVersion getJavaVersion() {
    try {
        // 0. Get java.version property from project's gradle.properties
        // The version value is like 1.8, 11, 17, 21, etc.
        // Also see: https://docs.gradle.org/current/javadoc/org/gradle/api/JavaVersion.html#toVersion-java.lang.Object-
        JavaVersion res

        def javaVersion = project.rootProject.findProperty("java.version")
        if (javaVersion != null) {
            res = JavaVersion.toVersion(javaVersion)
            logger.lifecycle("Get java version from project's gradle.properties: $javaVersion")
            if (res != null) {
                return res
            }
        }

        String javaHome
        // 1. read from JAVA_HOME environment variable
        javaHome = System.getenv("JAVA_HOME")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from JAVA_HOME: $javaHome")
            if (res != null) {
                return res
            }
        }

        // 2. read gradle.properties
        javaHome = project.rootProject.findProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from gradle.properties: $javaHome")
            if (res != null) {
                return res
            }
        }

        // 3. read from property with java.home
        javaHome = System.getProperty("java.home")
        if (res == null && javaHome != null) {
            res = getJavaFromHome(javaHome)
            logger.lifecycle("Get java version from java.home: $javaHome")
            if (res != null) {
                return res
            }
        }

        if (res != null) {
            return res
        }
    } catch (Exception e) {
        e.printStackTrace()
    }

    // last, use default version with current
    return JavaVersion.current()
}
android {
    namespace = "com.github.sososdk.aliyunpan_flutter_sdk_auth"

    compileSdk = 35

    compileOptions {
        def version = getJavaVersion()
        sourceCompatibility = version
        targetCompatibility = version
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        main.java.srcDirs += "$buildDir/generated/src/kotlin"
        test.java.srcDirs += "src/test/kotlin"
    }

    defaultConfig {
        minSdk = 21

        Map config = loadPubspec()
        Map aliyunpan = (Map) config.get("aliyunpan")
        if (aliyunpan) {
            String appid = (String) aliyunpan.get("app_id")
            if (appid) {
                manifestPlaceholders["aliyunpan-appid"] = appid
            }
        }
    }

    dependencies {
        testImplementation("org.jetbrains.kotlin:kotlin-test")
        testImplementation("org.mockito:mockito-core:5.0.0")
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
                events "passed", "skipped", "failed", "standardOut", "standardError"
                outputs.upToDateWhen {false}
                showStandardStreams = true
            }
        }
    }
}

Map loadPubspec() {
    def path = rootProject.projectDir.parent + File.separator + "pubspec.yaml"
    InputStream input = new FileInputStream(new File(path))
    Yaml yaml = new Yaml()
    return yaml.load(input)
}

tasks.register("generatePluginConfigFile") {
    doFirst {
        Map config = loadPubspec()
        Map aliyunpan = (Map) config.get("aliyunpan")
        String flutterActivity = ""
        if (aliyunpan) {
            Map android = (Map) aliyunpan.get("android")
            if (android) {
                def activity = android.get("flutter_activity")
                if (activity) {
                    flutterActivity = (String) activity
                }
            }
        }

        generatePluginConfig(flutterActivity)
    }
}

def generatePluginConfig(String flutterActivity) {
    File generateFolder = new File("${buildDir}/generated/src/kotlin/com/github/sososdk/aliyunpan_flutter_sdk_auth")
    if (!generateFolder.exists()) {
        generateFolder.mkdirs()
    }
    String source = """/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.github.sososdk.aliyunpan_flutter_sdk_auth

internal object PluginConfig {
    val flutterActivity: String = "${flutterActivity}"
}
"""
    file("${generateFolder.absolutePath}/PluginConfig.kt").text = source
}

android.libraryVariants.configureEach {
    it.registerGeneratedResFolders(project.files(new File("${buildDir}/generated/src/kotlin/com/github/sososdk/aliyunpan_flutter_sdk")).builtBy(generatePluginConfigFile))
}